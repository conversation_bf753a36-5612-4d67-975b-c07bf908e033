package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"chatport-go/config"
	"chatport-go/internal/client"
	"chatport-go/internal/errors"
	"chatport-go/internal/logger"
	"chatport-go/internal/server"
)

func main() {
	// Set up panic recovery
	defer func() {
		if r := errors.Recovery(); r != nil {
			if appErr, ok := r.(*errors.AppError); ok {
				logger.ErrorWithFields("Application panic", map[string]interface{}{
					"error": appErr.Error(),
					"stack": appErr.StackTrace,
				})
			}
			os.Exit(1)
		}
	}()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		logger.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize logger
	logger.Init(cfg)
	logger.Info("Starting ChatPort WhatsApp Service...")

	// Initialize WhatsApp client
	if err := client.InitClient(); err != nil {
		logger.Fatalf("Failed to initialize WhatsApp client: %v", err)
	}

	// Create and start HTTP server
	srv := server.New()

	// Start server in a goroutine
	go func() {
		if err := srv.Start(); err != nil {
			logger.Fatalf("Failed to start HTTP server: %v", err)
		}
	}()

	logger.Info("ChatPort service started successfully")
	logger.Infof("HTTP server listening on port %s", srv.GetPort())

	if client.Client != nil {
		logger.Infof("WhatsApp client status: connected=%v", client.Client.IsConnected())
	}

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down ChatPort service...")

	// Create a deadline for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown HTTP server
	if err := srv.Stop(ctx); err != nil {
		logger.Errorf("Error shutting down HTTP server: %v", err)
	} else {
		logger.Info("HTTP server stopped gracefully")
	}

	// Disconnect WhatsApp client
	if client.Client != nil {
		client.Client.Disconnect()
		logger.Info("WhatsApp client disconnected")
	}

	logger.Info("ChatPort service stopped")
}
