package config

import (
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

// tempEnv is a helper to temporarily set environment variables and reset them afterward.
func tempEnv(t *testing.T, envs map[string]string) {
	t.Helper()

	originalEnv := make(map[string]string)
	for k := range envs {
		if val, ok := os.LookupEnv(k); ok {
			originalEnv[k] = val
		} else {
			originalEnv[k] = ""
		}
	}

	for k, v := range envs {
		if err := os.Setenv(k, v); err != nil {
			t.Fatalf("Failed to set environment variable %q: %v", k, err)
		}
	}

	t.Cleanup(func() {
		for k := range envs {
			if originalVal, ok := originalEnv[k]; ok {
				if originalVal == "" {
					os.Unsetenv(k)
				} else {
					os.Setenv(k, originalVal)
				}
			}
		}
	})
}

// createTempFile creates a temporary .env file with given content in a temporary directory.
func createTempFile(t *testing.T, content string) (string, func()) {
	t.Helper()

	dir, err := os.MkdirTemp("", "envtest-*")
	if err != nil {
		t.Fatal("Failed to create temp dir:", err)
	}

	path := filepath.Join(dir, ".env")

	err = os.WriteFile(path, []byte(content), 0644)
	if err != nil {
		t.Fatal("Failed to write temp .env file:", err)
	}

	return path, func() {
		os.RemoveAll(dir)
	}
}

func TestLoadEnv(t *testing.T) {
	t.Run("loads_env_file_successfully", func(t *testing.T) {
		envContent := "KEY1=value1\nKEY2=value2"
		filePath, cleanup := createTempFile(t, envContent)
		defer cleanup()

		// Change working directory to temp dir where .env exists
		oldWd, _ := os.Getwd()
		defer os.Chdir(oldWd)
		os.Chdir(filepath.Dir(filePath))

		LoadEnv()

		val1 := os.Getenv("KEY1")
		val2 := os.Getenv("KEY2")

		if val1 != "value1" || val2 != "value2" {
			t.Errorf("Expected environment variables not loaded correctly")
		}
	})

	t.Run("logs_info_when_no_env_file", func(t *testing.T) {
		// Capture log output
		logOutput := captureLogs(t, func() {
			LoadEnv()
		})

		expected := "No .env file found, using system environment"
		if !strings.Contains(logOutput, expected) {
			t.Errorf("Expected log message containing %q, got %q", expected, logOutput)
		}
	})
}

// TestLoadEnvWithFile tests LoadEnv when a .env file exists
func TestLoadEnvWithFile(t *testing.T) {
	// Setup - create a temporary .env file
	dir := t.TempDir()
	envPath := filepath.Join(dir, ".env")
	err := os.WriteFile(envPath, []byte("TEST_KEY=test_value"), 0644)
	if err != nil {
		t.Fatalf("Failed to create .env file: %v", err)
	}

	// Change working directory to the temp dir
	oldWd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current working directory: %v", err)
	}
	defer os.Chdir(oldWd)
	os.Chdir(dir)

	// Test
	LoadEnv()

	// Verify
	if val := os.Getenv("TEST_KEY"); val != "test_value" {
		t.Errorf("Expected TEST_KEY to be 'test_value', got '%s'", val)
	}
}

// TestLoadEnvWithoutFile tests LoadEnv when no .env file exists
func TestLoadEnvWithoutFile(t *testing.T) {
	// Setup - use a clean temp dir with no .env file
	dir := t.TempDir()
	oldWd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current working directory: %v", err)
	}
	defer os.Chdir(oldWd)
	os.Chdir(dir)

	// Test - should not panic or fail
	LoadEnv()
}

func TestGetEnv(t *testing.T) {
	t.Run("returns_set_environment_variable", func(t *testing.T) {
		tempEnv(t, map[string]string{
			"TEST_VAR": "from_os",
		})

		got := GetEnv("TEST_VAR", "default")
		want := "from_os"

		if got != want {
			t.Errorf("GetEnv() = %v, want %v", got, want)
		}
	})

	t.Run("returns_fallback_if_environment_variable_not_set", func(t *testing.T) {
		// Ensure TEST_VAR is not set
		os.Unsetenv("TEST_VAR")

		got := GetEnv("TEST_VAR", "fallback_value")
		want := "fallback_value"

		if got != want {
			t.Errorf("GetEnv() = %v, want %v", got, want)
		}
	})

	t.Run("returns_empty_string_if_both_unset_and_no_fallback", func(t *testing.T) {
		os.Unsetenv("NON_EXISTENT_VAR")

		got := GetEnv("NON_EXISTENT_VAR", "")
		want := ""

		if got != want {
			t.Errorf("GetEnv() = %v, want %v", got, want)
		}
	})
}

// TestGetEnvWithSetVariable tests GetEnv when the variable is set
func TestGetEnvWithSetVariable(t *testing.T) {
	// Setup
	const testKey = "TEST_KEY_GETENV"
	const testValue = "test_value_123"
	os.Setenv(testKey, testValue)
	defer os.Unsetenv(testKey)

	// Test
	result := GetEnv(testKey, "default_value")

	// Verify
	if result != testValue {
		t.Errorf("Expected %s, got %s", testValue, result)
	}
}

// TestGetEnvWithUnsetVariable tests GetEnv when the variable is not set
func TestGetEnvWithUnsetVariable(t *testing.T) {
	// Setup
	const testKey = "NON_EXISTENT_KEY"
	const defaultValue = "default_value_456"
	os.Unsetenv(testKey) // Ensure it's not set

	// Test
	result := GetEnv(testKey, defaultValue)

	// Verify
	if result != defaultValue {
		t.Errorf("Expected default value %s, got %s", defaultValue, result)
	}
}

// TestGetEnvWithEmptyVariable tests GetEnv when the variable is set but empty
func TestGetEnvWithEmptyVariable(t *testing.T) {
	// Setup
	const testKey = "EMPTY_KEY"
	os.Setenv(testKey, "") // Set to empty string
	defer os.Unsetenv(testKey)

	const defaultValue = "default_value_789"

	// Test
	result := GetEnv(testKey, defaultValue)

	// Verify
	if result != "" {
		t.Errorf("Expected empty string, got %s", result)
	}
}

// TestLoadEnvWithMalformedFile tests LoadEnv with a malformed .env file
func TestLoadEnvWithMalformedFile(t *testing.T) {
	// Setup - create a malformed .env file
	dir := t.TempDir()
	envPath := filepath.Join(dir, ".env")
	err := os.WriteFile(envPath, []byte("THIS_IS_NOT_A_VALID_LINE\nTEST_KEY=test_value"), 0644)
	if err != nil {
		t.Fatalf("Failed to create .env file: %v", err)
	}

	// Change working directory to the temp dir
	oldWd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current working directory: %v", err)
	}
	defer os.Chdir(oldWd)
	os.Chdir(dir)

	// Test - should not panic
	LoadEnv()

	// Verify that valid entries are still loaded
	if val := os.Getenv("TEST_KEY"); val != "test_value" {
		t.Errorf("Expected TEST_KEY to be 'test_value', got '%s'", val)
	}
}

// TestGetEnvPriority tests that system env vars take precedence over .env file
func TestGetEnvPriority(t *testing.T) {
	// Setup - create .env file and set system env var
	dir := t.TempDir()
	envPath := filepath.Join(dir, ".env")
	err := os.WriteFile(envPath, []byte("PRIORITY_KEY=file_value"), 0644)
	if err != nil {
		t.Fatalf("Failed to create .env file: %v", err)
	}

	const testKey = "PRIORITY_KEY"
	const systemValue = "system_value"
	os.Setenv(testKey, systemValue)
	defer os.Unsetenv(testKey)

	// Change working directory to the temp dir
	oldWd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current working directory: %v", err)
	}
	defer os.Chdir(oldWd)
	os.Chdir(dir)

	// Load .env file
	LoadEnv()

	// Test GetEnv
	result := GetEnv(testKey, "default_value")

	// Verify system value takes precedence
	if result != systemValue {
		t.Errorf("Expected system value '%s', got '%s'", systemValue, result)
	}
}

// captureLogs captures log output from a function call.
func captureLogs(t *testing.T, f func()) string {
	t.Helper()

	// Redirect logs to a buffer
	oldOut := log.Writer()
	r, w, _ := os.Pipe()
	log.SetOutput(w)

	f() // Execute the function

	_ = w.Close() // Ensure all data is flushed
	log.SetOutput(oldOut)

	out, _ := io.ReadAll(r)
	return string(out)
}
