package handlers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"chatport-go/internal/client"
)

func TestHealthHandler(t *testing.T) {
	tests := []struct {
		name           string
		clientSetup    func()
		expectedStatus int
		expectedHealth string
	}{
		{
			name: "healthy when client connected",
			clientSetup: func() {
				// Mock a connected client
				// In a real test, we'd use dependency injection
				// For now, we'll test the handler logic
			},
			expectedStatus: http.StatusOK,
			expectedHealth: "ok",
		},
		{
			name: "degraded when client not connected",
			clientSetup: func() {
				client.Client = nil
			},
			expectedStatus: http.StatusServiceUnavailable,
			expectedHealth: "degraded",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			tt.clientSetup()

			// Create request
			req := httptest.NewRequest(http.MethodGet, "/api/health", nil)
			rr := httptest.NewRecorder()

			// Call handler
			HealthHandler(rr, req)

			// Check status code
			if rr.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", rr.Code, tt.expectedStatus)
			}

			// Check content type
			contentType := rr.Header().Get("Content-Type")
			if contentType != "application/json" {
				t.Errorf("Expected Content-Type application/json, got %s", contentType)
			}

			// Parse response
			var response HealthResponse
			err := json.NewDecoder(rr.Body).Decode(&response)
			if err != nil {
				t.Fatalf("Failed to decode response: %v", err)
			}

			// Check health status
			if response.Status != tt.expectedHealth {
				t.Errorf("Expected status %s, got %s", tt.expectedHealth, response.Status)
			}

			// Check required fields
			if response.Version == "" {
				t.Error("Expected version to be present")
			}

			if response.Timestamp.IsZero() {
				t.Error("Expected timestamp to be present")
			}

			// Check timestamp is recent (within last minute)
			if time.Since(response.Timestamp) > time.Minute {
				t.Error("Expected timestamp to be recent")
			}
		})
	}
}

func TestHealthResponse_Structure(t *testing.T) {
	// Test that the health response has the expected structure
	req := httptest.NewRequest(http.MethodGet, "/api/health", nil)
	rr := httptest.NewRecorder()

	HealthHandler(rr, req)

	var response HealthResponse
	err := json.NewDecoder(rr.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	// Check all required fields are present
	if response.Status == "" {
		t.Error("Status field is missing")
	}

	if response.Version == "" {
		t.Error("Version field is missing")
	}

	if response.Timestamp.IsZero() {
		t.Error("Timestamp field is missing")
	}

	// Check services structure
	// WhatsApp service should be present
	// We can't check the actual values without mocking, but we can check structure
}

func TestHealthHandler_JSONFormat(t *testing.T) {
	req := httptest.NewRequest(http.MethodGet, "/api/health", nil)
	rr := httptest.NewRecorder()

	HealthHandler(rr, req)

	// Verify response is valid JSON
	var result map[string]interface{}
	err := json.NewDecoder(rr.Body).Decode(&result)
	if err != nil {
		t.Fatalf("Response is not valid JSON: %v", err)
	}

	// Check expected top-level keys
	expectedKeys := []string{"status", "timestamp", "version", "services"}
	for _, key := range expectedKeys {
		if _, exists := result[key]; !exists {
			t.Errorf("Expected key %s not found in response", key)
		}
	}
}

func TestHealthHandler_HTTPMethod(t *testing.T) {
	// Test that handler works with GET method
	req := httptest.NewRequest(http.MethodGet, "/api/health", nil)
	rr := httptest.NewRecorder()

	HealthHandler(rr, req)

	if rr.Code == http.StatusMethodNotAllowed {
		t.Error("Handler should accept GET method")
	}
}

func TestHealthHandler_NoBody(t *testing.T) {
	// Test that handler works without request body
	req := httptest.NewRequest(http.MethodGet, "/api/health", nil)
	rr := httptest.NewRecorder()

	HealthHandler(rr, req)

	// Should not fail
	if rr.Code >= 500 {
		t.Errorf("Handler should not fail with empty body, got status %d", rr.Code)
	}
}
