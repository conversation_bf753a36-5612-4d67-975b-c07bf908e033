package main

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gorilla/websocket"
)

func main() {
	fmt.Println("Testing WebSocket connection with detailed debugging...")

	// Test 1: Check if the endpoint exists
	fmt.Println("\n1. Testing HTTP GET to /ws endpoint...")
	resp, err := http.Get("http://localhost:8081/ws")
	if err != nil {
		log.Printf("HTTP GET failed: %v", err)
	} else {
		fmt.Printf("HTTP GET response: %s\n", resp.Status)
		fmt.Printf("Headers: %v\n", resp.Header)
		resp.Body.Close()
	}

	// Test 2: Test WebSocket handshake with detailed headers
	fmt.Println("\n2. Testing WebSocket handshake...")
	
	dialer := websocket.Dialer{
		HandshakeTimeout: 10 * time.Second,
		// Enable detailed logging
		NetDial: nil,
	}

	// Add custom headers for debugging
	headers := http.Header{}
	headers.Set("Origin", "http://localhost:8081")
	headers.Set("User-Agent", "ChatPort-Debug-Client/1.0")

	fmt.Printf("Attempting to connect to: ws://localhost:8081/ws\n")
	fmt.Printf("Headers: %v\n", headers)

	conn, resp, err := dialer.Dial("ws://localhost:8081/ws", headers)
	if err != nil {
		fmt.Printf("❌ WebSocket connection failed: %v\n", err)
		if resp != nil {
			fmt.Printf("Response status: %s\n", resp.Status)
			fmt.Printf("Response headers: %v\n", resp.Header)
			
			// Read response body
			body := make([]byte, 1024)
			n, _ := resp.Body.Read(body)
			if n > 0 {
				fmt.Printf("Response body: %s\n", string(body[:n]))
			}
			resp.Body.Close()
		}
		return
	}
	defer conn.Close()

	fmt.Println("✅ WebSocket connection successful!")
	fmt.Printf("Response status: %s\n", resp.Status)
	fmt.Printf("Response headers: %v\n", resp.Header)

	// Test 3: Try to read welcome message
	fmt.Println("\n3. Testing message exchange...")
	
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	_, message, err := conn.ReadMessage()
	if err != nil {
		fmt.Printf("❌ Failed to read welcome message: %v\n", err)
		return
	}

	fmt.Printf("✅ Received message: %s\n", string(message))

	// Test 4: Send a ping
	fmt.Println("\n4. Testing ping...")
	
	pingMsg := `{"type":"ping","timestamp":"` + time.Now().Format(time.RFC3339) + `"}`
	err = conn.WriteMessage(websocket.TextMessage, []byte(pingMsg))
	if err != nil {
		fmt.Printf("❌ Failed to send ping: %v\n", err)
		return
	}

	// Read pong response
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	_, pongMessage, err := conn.ReadMessage()
	if err != nil {
		fmt.Printf("❌ Failed to read pong: %v\n", err)
		return
	}

	fmt.Printf("✅ Received pong: %s\n", string(pongMessage))
	fmt.Println("\n🎉 All WebSocket tests passed!")
}
