package handlers

import (
	"encoding/json"
	"net/http"
	"runtime"
	"time"

	"chatport-go/internal/client"
)

// StatusResponse represents detailed status information
type StatusResponse struct {
	Application ApplicationInfo `json:"application"`
	System      SystemInfo      `json:"system"`
	WhatsApp    WhatsAppInfo    `json:"whatsapp"`
	Timestamp   time.Time       `json:"timestamp"`
}

// ApplicationInfo contains application-level information
type ApplicationInfo struct {
	Name    string `json:"name"`
	Version string `json:"version"`
	Uptime  string `json:"uptime"`
}

// SystemInfo contains system-level information
type SystemInfo struct {
	GoVersion    string `json:"go_version"`
	NumGoroutine int    `json:"num_goroutine"`
	NumCPU       int    `json:"num_cpu"`
}

// WhatsAppInfo contains WhatsApp-specific status information
type WhatsAppInfo struct {
	Connected bool   `json:"connected"`
	JID       string `json:"jid,omitempty"`
	Status    string `json:"status"`
}

var startTime = time.Now()

// StatusHandler handles GET /api/status requests
func StatusHandler(w http.ResponseWriter, r *http.Request) {
	var whatsappInfo WhatsAppInfo
	
	if client.Client != nil {
		whatsappInfo.Connected = client.Client.IsConnected()
		whatsappInfo.JID = client.Client.GetJID()
		
		if whatsappInfo.Connected {
			whatsappInfo.Status = "connected"
		} else {
			whatsappInfo.Status = "disconnected"
		}
	} else {
		whatsappInfo.Status = "not_initialized"
	}
	
	uptime := time.Since(startTime)
	
	response := StatusResponse{
		Application: ApplicationInfo{
			Name:    "ChatPort WhatsApp Service",
			Version: "1.0.0",
			Uptime:  uptime.String(),
		},
		System: SystemInfo{
			GoVersion:    runtime.Version(),
			NumGoroutine: runtime.NumGoroutine(),
			NumCPU:       runtime.NumCPU(),
		},
		WhatsApp:  whatsappInfo,
		Timestamp: time.Now(),
	}
	
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}
