package handlers

import (
	"encoding/json"
	"log"
	"net/http"

	"chatport-go/internal/client"
)

type SendRequest struct {
	Number  string `json:"number"`
	Message string `json:"message"`
}

func SendHandler(w http.ResponseWriter, r *http.Request) {
	var payload SendRequest
	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}
	log.Printf("Sending to %s: %s", payload.Number, payload.Message)

	err = client.SendMessage(payload.Number, payload.Message)
	if err != nil {
		http.Error(w, "Failed to send", http.StatusInternalServerError)
		return
	}

	w.<PERSON><PERSON><PERSON>ead<PERSON>(http.StatusOK)
}
