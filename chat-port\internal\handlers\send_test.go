package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"chatport-go/internal/client"
)

// MockWhatsAppClient is a mock implementation for testing
type MockWhatsAppClient struct {
	connected    bool
	sendError    error
	messagesSent []MockMessage
}

type MockMessage struct {
	Number  string
	Message string
}

func (m *MockWhatsAppClient) IsConnected() bool {
	return m.connected
}

func (m *MockWhatsAppClient) SendMessage(number, message string) error {
	if m.sendError != nil {
		return m.sendError
	}
	m.messagesSent = append(m.messagesSent, MockMessage{
		Number:  number,
		Message: message,
	})
	return nil
}

func TestSendHandler(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    interface{}
		clientSetup    func()
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "invalid JSON payload",
			requestBody:    "invalid json",
			clientSetup:    func() { /* no setup needed */ },
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid JSON payload",
		},
		{
			name: "missing number field",
			requestBody: SendRequest{
				Message: "Hello, World!",
			},
			clientSetup:    func() { /* no setup needed */ },
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Number and message are required",
		},
		{
			name: "missing message field",
			requestBody: SendRequest{
				Number: "1234567890",
			},
			clientSetup:    func() { /* no setup needed */ },
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Number and message are required",
		},
		{
			name: "client not initialized",
			requestBody: SendRequest{
				Number:  "1234567890",
				Message: "Hello, World!",
			},
			clientSetup: func() {
				client.Client = nil
			},
			expectedStatus: http.StatusServiceUnavailable,
			expectedError:  "WhatsApp client not initialized",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup client state
			tt.clientSetup()

			// Create request body
			var body []byte
			var err error
			if str, ok := tt.requestBody.(string); ok {
				body = []byte(str)
			} else {
				body, err = json.Marshal(tt.requestBody)
				if err != nil {
					t.Fatalf("Failed to marshal request body: %v", err)
				}
			}

			// Create request
			req := httptest.NewRequest(http.MethodPost, "/api/send", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			// Create response recorder
			rr := httptest.NewRecorder()

			// Call handler
			SendHandler(rr, req)

			// Check status code
			if rr.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
			}

			// Check error message if expected
			if tt.expectedError != "" {
				var response SendResponse
				err := json.NewDecoder(rr.Body).Decode(&response)
				if err != nil {
					t.Fatalf("Failed to decode response: %v", err)
				}

				if response.Error != tt.expectedError {
					t.Errorf("Expected error %q, got %q", tt.expectedError, response.Error)
				}

				if response.Success {
					t.Error("Expected success to be false for error cases")
				}
			}

			// Check success response
			if tt.expectedStatus == http.StatusOK {
				var response SendResponse
				err := json.NewDecoder(rr.Body).Decode(&response)
				if err != nil {
					t.Fatalf("Failed to decode response: %v", err)
				}

				if !response.Success {
					t.Error("Expected success to be true")
				}

				if response.Message == "" {
					t.Error("Expected success message to be present")
				}
			}
		})
	}
}

func TestRespondWithJSON(t *testing.T) {
	tests := []struct {
		name           string
		payload        interface{}
		expectedStatus int
	}{
		{
			name: "valid payload",
			payload: SendResponse{
				Success: true,
				Message: "Test message",
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:           "nil payload",
			payload:        nil,
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rr := httptest.NewRecorder()

			respondWithJSON(rr, tt.expectedStatus, tt.payload)

			if rr.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
			}

			contentType := rr.Header().Get("Content-Type")
			if contentType != "application/json" {
				t.Errorf("Expected Content-Type application/json, got %s", contentType)
			}

			// Verify JSON is valid
			var result interface{}
			err := json.NewDecoder(rr.Body).Decode(&result)
			if err != nil {
				t.Errorf("Response is not valid JSON: %v", err)
			}
		})
	}
}

func TestRespondWithError(t *testing.T) {
	tests := []struct {
		name           string
		code           int
		message        string
		expectedStatus int
	}{
		{
			name:           "bad request error",
			code:           http.StatusBadRequest,
			message:        "Invalid input",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "internal server error",
			code:           http.StatusInternalServerError,
			message:        "Something went wrong",
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rr := httptest.NewRecorder()

			respondWithError(rr, tt.code, tt.message)

			if rr.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
			}

			var response SendResponse
			err := json.NewDecoder(rr.Body).Decode(&response)
			if err != nil {
				t.Fatalf("Failed to decode response: %v", err)
			}

			if response.Success {
				t.Error("Expected success to be false")
			}

			if response.Error != tt.message {
				t.Errorf("Expected error message %q, got %q", tt.message, response.Error)
			}
		})
	}
}

func TestSendRequest_JSONMarshaling(t *testing.T) {
	request := SendRequest{
		Number:  "1234567890",
		Message: "Test message",
	}

	// Test marshaling
	data, err := json.Marshal(request)
	if err != nil {
		t.Fatalf("Failed to marshal SendRequest: %v", err)
	}

	// Test unmarshaling
	var unmarshaled SendRequest
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal SendRequest: %v", err)
	}

	if unmarshaled.Number != request.Number {
		t.Errorf("Expected number %s, got %s", request.Number, unmarshaled.Number)
	}

	if unmarshaled.Message != request.Message {
		t.Errorf("Expected message %s, got %s", request.Message, unmarshaled.Message)
	}
}

func TestSendResponse_JSONMarshaling(t *testing.T) {
	response := SendResponse{
		Success: true,
		Message: "Success message",
		Error:   "Error message",
	}

	// Test marshaling
	data, err := json.Marshal(response)
	if err != nil {
		t.Fatalf("Failed to marshal SendResponse: %v", err)
	}

	// Test unmarshaling
	var unmarshaled SendResponse
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal SendResponse: %v", err)
	}

	if unmarshaled.Success != response.Success {
		t.Errorf("Expected success %v, got %v", response.Success, unmarshaled.Success)
	}

	if unmarshaled.Message != response.Message {
		t.Errorf("Expected message %s, got %s", response.Message, unmarshaled.Message)
	}

	if unmarshaled.Error != response.Error {
		t.Errorf("Expected error %s, got %s", response.Error, unmarshaled.Error)
	}
}
