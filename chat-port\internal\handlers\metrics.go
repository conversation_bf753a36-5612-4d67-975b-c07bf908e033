package handlers

import (
	"encoding/json"
	"net/http"
	"time"

	"chatport-go/internal/monitoring"
)

// MetricsResponse represents the metrics endpoint response
type MetricsResponse struct {
	Metrics     monitoring.Metrics `json:"metrics"`
	Uptime      string             `json:"uptime"`
	Rates       MetricsRates       `json:"rates"`
	Timestamp   time.Time          `json:"timestamp"`
}

// MetricsRates represents success rates for various operations
type MetricsRates struct {
	MessageSuccessRate   float64 `json:"message_success_rate"`
	AIServiceSuccessRate float64 `json:"ai_service_success_rate"`
	HTTPSuccessRate      float64 `json:"http_success_rate"`
}

// MetricsHandler handles GET /api/metrics requests
func MetricsHandler(w http.ResponseWriter, r *http.Request) {
	metrics := monitoring.GetMetrics()
	snapshot := metrics.GetSnapshot()
	
	response := MetricsResponse{
		Metrics:   snapshot,
		Uptime:    metrics.GetUptime().String(),
		Rates: MetricsRates{
			MessageSuccessRate:   metrics.GetMessageSuccessRate(),
			AIServiceSuccessRate: metrics.GetAIServiceSuccessRate(),
			HTTPSuccessRate:      metrics.GetHTTPSuccessRate(),
		},
		Timestamp: time.Now(),
	}
	
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}
