# ChatPort - WhatsApp Service

ChatPort is a professional-grade Go service that provides WhatsApp messaging capabilities for the Wellbot pharmacy assistant system. It acts as the communication bridge between customers and the AI-powered pharmacy assistant.

## 🏗️ Architecture

ChatPort is part of a microservice architecture:

- **ChatPort (Go)**: WhatsApp interface using whatsmeow library
- **Med-Intel (Rust)**: AI service for intelligent responses
- **Embed-RX (Rust)**: Data processing and embedding generation

## 🚀 Features

- **WhatsApp Integration**: Full WhatsApp Business API support via whatsmeow
- **QR Code Authentication**: Easy setup with QR code scanning
- **Message Processing**: Automatic forwarding to AI service
- **Health Monitoring**: Comprehensive health checks and metrics
- **Graceful Shutdown**: Proper cleanup and connection management
- **Structured Logging**: Configurable logging with JSON/text formats
- **Error Handling**: Comprehensive error handling with stack traces
- **Configuration Management**: Environment-based configuration
- **Production Ready**: Timeouts, retries, and monitoring

## 📋 Prerequisites

- Go 1.24.2 or later
- SQLite3 (for session storage)
- Access to WhatsApp Business API
- Running Med-Intel service (Rust AI service)

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd chat-port
   ```

2. **Install dependencies**:
   ```bash
   go mod download
   ```

3. **Configure environment** (copy and edit):
   ```bash
   cp .env.example .env
   ```

4. **Build the application**:
   ```bash
   go build -o chatport ./cmd
   ```

## ⚙️ Configuration

Configuration is managed through environment variables. See `.env.example` for all available options:

### Server Configuration
- `SERVER_PORT`: HTTP server port (default: 8081)
- `SERVER_READ_TIMEOUT`: Request read timeout (default: 15s)
- `SERVER_WRITE_TIMEOUT`: Response write timeout (default: 15s)
- `SERVER_IDLE_TIMEOUT`: Connection idle timeout (default: 60s)

### WhatsApp Configuration
- `WHATSAPP_DB_PATH`: Session database path (default: file:session.db?_foreign_keys=on)
- `WHATSAPP_QR_TIMEOUT`: QR code scan timeout (default: 2m)
- `WHATSAPP_RECONNECT_DELAY`: Reconnection delay (default: 5s)
- `WHATSAPP_MAX_RECONNECTS`: Maximum reconnection attempts (default: 5)

### AI Service Configuration
- `AI_SERVICE_URL`: Med-Intel service URL (default: http://localhost:8080)
- `AI_SERVICE_TIMEOUT`: Request timeout (default: 30s)
- `AI_SERVICE_RETRY_ATTEMPTS`: Retry attempts (default: 3)
- `AI_SERVICE_RETRY_DELAY`: Retry delay (default: 1s)

### Logging Configuration
- `LOG_LEVEL`: Logging level - debug, info, warn, error (default: info)
- `LOG_FORMAT`: Log format - text, json (default: text)

## 🚀 Usage

### Starting the Service

```bash
# Using the binary
./chatport

# Or using go run
go run ./cmd
```

### First Time Setup

1. Start the service
2. Scan the QR code displayed in the console with WhatsApp
3. The service will automatically connect and start processing messages

### API Endpoints

#### Send Message
```http
POST /api/send
Content-Type: application/json

{
  "number": "**********",
  "message": "Hello from ChatPort!"
}
```

#### Health Check
```http
GET /api/health
```

#### Service Status
```http
GET /api/status
```

#### Metrics
```http
GET /api/metrics
```

## 📊 Monitoring

ChatPort provides comprehensive monitoring capabilities:

### Health Checks
- WhatsApp connection status
- Service availability
- Component health

### Metrics
- Messages received/sent/failed
- AI service call statistics
- HTTP request metrics
- Success rates and uptime

### Logging
- Structured logging with configurable levels
- Request/response logging
- Error tracking with stack traces
- Performance metrics

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run tests with verbose output
go test -v ./...

# Run specific package tests
go test ./internal/handlers
```

## 🏗️ Project Structure

```
chat-port/
├── cmd/                    # Application entry point
│   └── main.go
├── config/                 # Configuration management
│   ├── config.go
│   └── config_test.go
├── internal/               # Internal packages
│   ├── client/            # WhatsApp client
│   ├── errors/            # Error handling
│   ├── handlers/          # HTTP handlers
│   ├── logger/            # Logging utilities
│   ├── monitoring/        # Metrics and monitoring
│   └── server/            # HTTP server
├── .env.example           # Environment configuration template
├── go.mod                 # Go module definition
├── go.sum                 # Go module checksums
└── README.md              # This file
```

## 🔧 Development

### Adding New Features

1. Create feature branch
2. Implement with tests
3. Update documentation
4. Submit pull request

### Code Standards

- Follow Go best practices
- Write comprehensive tests
- Use structured logging
- Handle errors properly
- Document public APIs

## 🚨 Troubleshooting

### Common Issues

**QR Code Not Displaying**
- Check console output for QR code
- Ensure WhatsApp is not already logged in elsewhere
- Verify network connectivity

**Connection Failures**
- Check AI service availability
- Verify configuration settings
- Review logs for error details

**Message Delivery Issues**
- Confirm WhatsApp connection status
- Check recipient number format
- Review error logs

### Logs and Debugging

Enable debug logging:
```bash
export LOG_LEVEL=debug
./chatport
```

Check health status:
```bash
curl http://localhost:8081/api/health
```

## 📄 License

This project is part of the Wellbot pharmacy assistant system.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For support and questions, please refer to the project documentation or contact the development team.
