package monitoring

import (
	"sync"
	"time"
)

// Metrics holds application metrics
type Metrics struct {
	mu                    sync.RWMutex
	MessagesReceived      int64     `json:"messages_received"`
	MessagesSent          int64     `json:"messages_sent"`
	MessagesFailed        int64     `json:"messages_failed"`
	AIServiceCalls        int64     `json:"ai_service_calls"`
	AIServiceFailures     int64     `json:"ai_service_failures"`
	HTTPRequests          int64     `json:"http_requests"`
	HTTPErrors            int64     `json:"http_errors"`
	WhatsAppReconnects    int64     `json:"whatsapp_reconnects"`
	LastMessageTime       time.Time `json:"last_message_time"`
	LastAIServiceCall     time.Time `json:"last_ai_service_call"`
	StartTime             time.Time `json:"start_time"`
}

var globalMetrics *Metrics
var once sync.Once

// GetMetrics returns the global metrics instance
func GetMetrics() *Metrics {
	once.Do(func() {
		globalMetrics = &Metrics{
			StartTime: time.Now(),
		}
	})
	return globalMetrics
}

// IncrementMessagesReceived increments the messages received counter
func (m *Metrics) IncrementMessagesReceived() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.MessagesReceived++
	m.LastMessageTime = time.Now()
}

// IncrementMessagesSent increments the messages sent counter
func (m *Metrics) IncrementMessagesSent() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.MessagesSent++
}

// IncrementMessagesFailed increments the messages failed counter
func (m *Metrics) IncrementMessagesFailed() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.MessagesFailed++
}

// IncrementAIServiceCalls increments the AI service calls counter
func (m *Metrics) IncrementAIServiceCalls() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.AIServiceCalls++
	m.LastAIServiceCall = time.Now()
}

// IncrementAIServiceFailures increments the AI service failures counter
func (m *Metrics) IncrementAIServiceFailures() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.AIServiceFailures++
}

// IncrementHTTPRequests increments the HTTP requests counter
func (m *Metrics) IncrementHTTPRequests() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.HTTPRequests++
}

// IncrementHTTPErrors increments the HTTP errors counter
func (m *Metrics) IncrementHTTPErrors() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.HTTPErrors++
}

// IncrementWhatsAppReconnects increments the WhatsApp reconnects counter
func (m *Metrics) IncrementWhatsAppReconnects() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.WhatsAppReconnects++
}

// GetSnapshot returns a snapshot of current metrics
func (m *Metrics) GetSnapshot() Metrics {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	return Metrics{
		MessagesReceived:   m.MessagesReceived,
		MessagesSent:       m.MessagesSent,
		MessagesFailed:     m.MessagesFailed,
		AIServiceCalls:     m.AIServiceCalls,
		AIServiceFailures:  m.AIServiceFailures,
		HTTPRequests:       m.HTTPRequests,
		HTTPErrors:         m.HTTPErrors,
		WhatsAppReconnects: m.WhatsAppReconnects,
		LastMessageTime:    m.LastMessageTime,
		LastAIServiceCall:  m.LastAIServiceCall,
		StartTime:          m.StartTime,
	}
}

// GetUptime returns the application uptime
func (m *Metrics) GetUptime() time.Duration {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return time.Since(m.StartTime)
}

// GetMessageSuccessRate returns the message success rate as a percentage
func (m *Metrics) GetMessageSuccessRate() float64 {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	total := m.MessagesSent + m.MessagesFailed
	if total == 0 {
		return 100.0
	}
	
	return (float64(m.MessagesSent) / float64(total)) * 100.0
}

// GetAIServiceSuccessRate returns the AI service success rate as a percentage
func (m *Metrics) GetAIServiceSuccessRate() float64 {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	if m.AIServiceCalls == 0 {
		return 100.0
	}
	
	successful := m.AIServiceCalls - m.AIServiceFailures
	return (float64(successful) / float64(m.AIServiceCalls)) * 100.0
}

// GetHTTPSuccessRate returns the HTTP success rate as a percentage
func (m *Metrics) GetHTTPSuccessRate() float64 {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	if m.HTTPRequests == 0 {
		return 100.0
	}
	
	successful := m.HTTPRequests - m.HTTPErrors
	return (float64(successful) / float64(m.HTTPRequests)) * 100.0
}

// Reset resets all metrics (useful for testing)
func (m *Metrics) Reset() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.MessagesReceived = 0
	m.MessagesSent = 0
	m.MessagesFailed = 0
	m.AIServiceCalls = 0
	m.AIServiceFailures = 0
	m.HTTPRequests = 0
	m.HTTPErrors = 0
	m.WhatsAppReconnects = 0
	m.LastMessageTime = time.Time{}
	m.LastAIServiceCall = time.Time{}
	m.StartTime = time.Now()
}

// Global convenience functions
func IncrementMessagesReceived() {
	GetMetrics().IncrementMessagesReceived()
}

func IncrementMessagesSent() {
	GetMetrics().IncrementMessagesSent()
}

func IncrementMessagesFailed() {
	GetMetrics().IncrementMessagesFailed()
}

func IncrementAIServiceCalls() {
	GetMetrics().IncrementAIServiceCalls()
}

func IncrementAIServiceFailures() {
	GetMetrics().IncrementAIServiceFailures()
}

func IncrementHTTPRequests() {
	GetMetrics().IncrementHTTPRequests()
}

func IncrementHTTPErrors() {
	GetMetrics().IncrementHTTPErrors()
}

func IncrementWhatsAppReconnects() {
	GetMetrics().IncrementWhatsAppReconnects()
}
