package errors

import (
	"fmt"
	"runtime"
)

// ErrorType represents different types of errors
type ErrorType string

const (
	// ErrorTypeValidation represents validation errors
	ErrorTypeValidation ErrorType = "validation"
	
	// ErrorTypeWhatsApp represents WhatsApp-related errors
	ErrorTypeWhatsApp ErrorType = "whatsapp"
	
	// ErrorTypeAIService represents AI service-related errors
	ErrorTypeAIService ErrorType = "ai_service"
	
	// ErrorTypeHTTP represents HTTP-related errors
	ErrorTypeHTTP ErrorType = "http"
	
	// ErrorTypeConfig represents configuration errors
	ErrorTypeConfig ErrorType = "config"
	
	// ErrorTypeInternal represents internal system errors
	ErrorTypeInternal ErrorType = "internal"
)

// AppError represents an application error with context
type AppError struct {
	Type       ErrorType              `json:"type"`
	Message    string                 `json:"message"`
	Cause      error                  `json:"cause,omitempty"`
	Context    map[string]interface{} `json:"context,omitempty"`
	StackTrace string                 `json:"stack_trace,omitempty"`
}

// Error implements the error interface
func (e *AppError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Type, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

// Unwrap returns the underlying cause
func (e *AppError) Unwrap() error {
	return e.Cause
}

// WithContext adds context to the error
func (e *AppError) WithContext(key string, value interface{}) *AppError {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	e.Context[key] = value
	return e
}

// WithStackTrace adds stack trace to the error
func (e *AppError) WithStackTrace() *AppError {
	e.StackTrace = getStackTrace()
	return e
}

// New creates a new AppError
func New(errorType ErrorType, message string) *AppError {
	return &AppError{
		Type:    errorType,
		Message: message,
	}
}

// Newf creates a new AppError with formatted message
func Newf(errorType ErrorType, format string, args ...interface{}) *AppError {
	return &AppError{
		Type:    errorType,
		Message: fmt.Sprintf(format, args...),
	}
}

// Wrap wraps an existing error with additional context
func Wrap(err error, errorType ErrorType, message string) *AppError {
	return &AppError{
		Type:    errorType,
		Message: message,
		Cause:   err,
	}
}

// Wrapf wraps an existing error with formatted message
func Wrapf(err error, errorType ErrorType, format string, args ...interface{}) *AppError {
	return &AppError{
		Type:    errorType,
		Message: fmt.Sprintf(format, args...),
		Cause:   err,
	}
}

// Convenience functions for common error types

// NewValidationError creates a validation error
func NewValidationError(message string) *AppError {
	return New(ErrorTypeValidation, message)
}

// NewValidationErrorf creates a formatted validation error
func NewValidationErrorf(format string, args ...interface{}) *AppError {
	return Newf(ErrorTypeValidation, format, args...)
}

// NewWhatsAppError creates a WhatsApp error
func NewWhatsAppError(message string) *AppError {
	return New(ErrorTypeWhatsApp, message)
}

// NewWhatsAppErrorf creates a formatted WhatsApp error
func NewWhatsAppErrorf(format string, args ...interface{}) *AppError {
	return Newf(ErrorTypeWhatsApp, format, args...)
}

// WrapWhatsAppError wraps an error as a WhatsApp error
func WrapWhatsAppError(err error, message string) *AppError {
	return Wrap(err, ErrorTypeWhatsApp, message)
}

// NewAIServiceError creates an AI service error
func NewAIServiceError(message string) *AppError {
	return New(ErrorTypeAIService, message)
}

// NewAIServiceErrorf creates a formatted AI service error
func NewAIServiceErrorf(format string, args ...interface{}) *AppError {
	return Newf(ErrorTypeAIService, format, args...)
}

// WrapAIServiceError wraps an error as an AI service error
func WrapAIServiceError(err error, message string) *AppError {
	return Wrap(err, ErrorTypeAIService, message)
}

// NewHTTPError creates an HTTP error
func NewHTTPError(message string) *AppError {
	return New(ErrorTypeHTTP, message)
}

// NewHTTPErrorf creates a formatted HTTP error
func NewHTTPErrorf(format string, args ...interface{}) *AppError {
	return Newf(ErrorTypeHTTP, format, args...)
}

// NewConfigError creates a configuration error
func NewConfigError(message string) *AppError {
	return New(ErrorTypeConfig, message)
}

// NewConfigErrorf creates a formatted configuration error
func NewConfigErrorf(format string, args ...interface{}) *AppError {
	return Newf(ErrorTypeConfig, format, args...)
}

// WrapConfigError wraps an error as a configuration error
func WrapConfigError(err error, message string) *AppError {
	return Wrap(err, ErrorTypeConfig, message)
}

// NewInternalError creates an internal error
func NewInternalError(message string) *AppError {
	return New(ErrorTypeInternal, message).WithStackTrace()
}

// NewInternalErrorf creates a formatted internal error
func NewInternalErrorf(format string, args ...interface{}) *AppError {
	return Newf(ErrorTypeInternal, format, args...).WithStackTrace()
}

// WrapInternalError wraps an error as an internal error
func WrapInternalError(err error, message string) *AppError {
	return Wrap(err, ErrorTypeInternal, message).WithStackTrace()
}

// IsType checks if an error is of a specific type
func IsType(err error, errorType ErrorType) bool {
	if appErr, ok := err.(*AppError); ok {
		return appErr.Type == errorType
	}
	return false
}

// GetType returns the error type if it's an AppError
func GetType(err error) ErrorType {
	if appErr, ok := err.(*AppError); ok {
		return appErr.Type
	}
	return ErrorTypeInternal
}

// getStackTrace captures the current stack trace
func getStackTrace() string {
	buf := make([]byte, 1024)
	for {
		n := runtime.Stack(buf, false)
		if n < len(buf) {
			return string(buf[:n])
		}
		buf = make([]byte, 2*len(buf))
	}
}

// Recovery function for panic recovery
func Recovery() interface{} {
	if r := recover(); r != nil {
		return NewInternalErrorf("panic recovered: %v", r)
	}
	return nil
}
