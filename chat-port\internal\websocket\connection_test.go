package websocket

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gorilla/websocket"
)

// TestWebSocketConnectionIssue specifically tests the WebSocket connection issue
// that was reported: "WebSocket connection to 'ws://localhost:8081/ws' failed"
func TestWebSocketConnectionIssue(t *testing.T) {
	t.Log("Testing WebSocket connection issue resolution...")

	// Create hub and handler (simulating the server setup)
	hub := NewHub(DefaultHubConfig())
	go hub.Run()
	defer hub.Stop()

	handler := NewHandler(hub, nil)

	// Create test server that mimics the actual server setup
	mux := http.NewServeMux()
	mux.HandleFunc("/ws", handler.ServeHTTP)
	mux.HandleFunc("/api/ws/stats", handler.WebSocketStatsHandler)

	server := httptest.NewServer(mux)
	defer server.Close()

	t.Logf("Test server started at: %s", server.URL)

	// Test 1: Verify WebSocket stats endpoint works
	t.Run("WebSocket stats endpoint", func(t *testing.T) {
		resp, err := http.Get(server.URL + "/api/ws/stats")
		if err != nil {
			t.Fatalf("Failed to get WebSocket stats: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}

		var stats map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&stats)
		if err != nil {
			t.Fatalf("Failed to decode stats response: %v", err)
		}

		// Verify stats structure
		if _, exists := stats["hub_stats"]; !exists {
			t.Error("Expected hub_stats in response")
		}

		if _, exists := stats["clients"]; !exists {
			t.Error("Expected clients in response")
		}

		if _, exists := stats["config"]; !exists {
			t.Error("Expected config in response")
		}

		t.Log("✅ WebSocket stats endpoint working correctly")
	})

	// Test 2: Verify WebSocket connection works
	t.Run("WebSocket connection", func(t *testing.T) {
		wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/ws"
		t.Logf("Connecting to WebSocket URL: %s", wsURL)

		dialer := websocket.Dialer{
			HandshakeTimeout: 5 * time.Second,
		}

		conn, resp, err := dialer.Dial(wsURL, nil)
		if err != nil {
			t.Fatalf("Failed to connect to WebSocket: %v (HTTP Response: %v)", err, resp)
		}
		defer conn.Close()

		t.Log("✅ WebSocket connection established successfully")

		// Test 3: Verify welcome message is received
		conn.SetReadDeadline(time.Now().Add(2 * time.Second))
		_, message, err := conn.ReadMessage()
		if err != nil {
			t.Fatalf("Failed to read welcome message: %v", err)
		}

		var welcomeMsg map[string]interface{}
		err = json.Unmarshal(message, &welcomeMsg)
		if err != nil {
			t.Fatalf("Failed to parse welcome message: %v", err)
		}

		if welcomeMsg["type"] != "welcome" {
			t.Errorf("Expected welcome message, got: %v", welcomeMsg["type"])
		}

		t.Log("✅ Welcome message received correctly")

		// Test 4: Verify client is registered in hub
		time.Sleep(10 * time.Millisecond) // Allow registration to process

		if hub.GetClientCount() != 1 {
			t.Errorf("Expected 1 client registered, got %d", hub.GetClientCount())
		}

		t.Log("✅ Client registered in hub correctly")

		// Test 5: Verify subscription functionality
		subscribeMsg := map[string]interface{}{
			"type":      "subscribe",
			"timestamp": time.Now().Format(time.RFC3339),
			"data": map[string]interface{}{
				"action":       "subscribe",
				"subscription": "whatsapp",
			},
		}

		subscribeData, err := json.Marshal(subscribeMsg)
		if err != nil {
			t.Fatalf("Failed to marshal subscribe message: %v", err)
		}

		err = conn.WriteMessage(websocket.TextMessage, subscribeData)
		if err != nil {
			t.Fatalf("Failed to send subscribe message: %v", err)
		}

		t.Log("✅ Subscription message sent successfully")

		// Test 6: Verify ping-pong functionality
		pingMsg := map[string]interface{}{
			"type":      "ping",
			"timestamp": time.Now().Format(time.RFC3339),
		}

		pingData, err := json.Marshal(pingMsg)
		if err != nil {
			t.Fatalf("Failed to marshal ping message: %v", err)
		}

		err = conn.WriteMessage(websocket.TextMessage, pingData)
		if err != nil {
			t.Fatalf("Failed to send ping message: %v", err)
		}

		// Read pong response
		conn.SetReadDeadline(time.Now().Add(2 * time.Second))
		_, pongMessage, err := conn.ReadMessage()
		if err != nil {
			t.Fatalf("Failed to read pong message: %v", err)
		}

		var pongMsg map[string]interface{}
		err = json.Unmarshal(pongMessage, &pongMsg)
		if err != nil {
			t.Fatalf("Failed to parse pong message: %v", err)
		}

		if pongMsg["type"] != "pong" {
			t.Errorf("Expected pong message, got: %v", pongMsg["type"])
		}

		t.Log("✅ Ping-pong functionality working correctly")
	})

	// Test 7: Verify stats reflect the connection
	t.Run("Stats reflect connection", func(t *testing.T) {
		resp, err := http.Get(server.URL + "/api/ws/stats")
		if err != nil {
			t.Fatalf("Failed to get updated WebSocket stats: %v", err)
		}
		defer resp.Body.Close()

		var stats map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&stats)
		if err != nil {
			t.Fatalf("Failed to decode updated stats response: %v", err)
		}

		// Verify stats show the connection
		if hubStats, ok := stats["hub_stats"].(map[string]interface{}); ok {
			if currentConnections, ok := hubStats["current_connections"].(float64); ok {
				if int(currentConnections) != 1 {
					t.Errorf("Expected 1 current connection in stats, got %v", currentConnections)
				}
			}
		}

		if clients, ok := stats["clients"].([]interface{}); ok {
			if len(clients) != 1 {
				t.Errorf("Expected 1 client in stats, got %d", len(clients))
			}
		}

		t.Log("✅ Stats correctly reflect the WebSocket connection")
	})

	t.Log("🎉 All WebSocket connection tests passed - issue resolved!")
}

// TestWebSocketConnectionFailureScenarios tests various failure scenarios
func TestWebSocketConnectionFailureScenarios(t *testing.T) {
	// Test 1: Connection to non-existent endpoint
	t.Run("Non-existent endpoint", func(t *testing.T) {
		_, _, err := websocket.DefaultDialer.Dial("ws://localhost:9999/ws", nil)
		if err == nil {
			t.Error("Expected connection to fail for non-existent endpoint")
		}
		t.Logf("✅ Correctly failed to connect to non-existent endpoint: %v", err)
	})

	// Test 2: Connection to wrong path
	t.Run("Wrong path", func(t *testing.T) {
		hub := NewHub(DefaultHubConfig())
		handler := NewHandler(hub, nil)
		server := httptest.NewServer(handler)
		defer server.Close()

		wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/wrong-path"
		_, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
		if err == nil {
			t.Error("Expected connection to fail for wrong path")
		}
		t.Logf("✅ Correctly failed to connect to wrong path: %v", err)
	})

	// Test 3: HTTP request to WebSocket endpoint (should fail)
	t.Run("HTTP to WebSocket endpoint", func(t *testing.T) {
		hub := NewHub(DefaultHubConfig())
		handler := NewHandler(hub, nil)
		server := httptest.NewServer(handler)
		defer server.Close()

		// Regular HTTP request should fail
		resp, err := http.Get(server.URL + "/")
		if err == nil {
			resp.Body.Close()
			if resp.StatusCode == http.StatusOK {
				t.Error("Expected HTTP request to WebSocket endpoint to fail")
			}
		}
		t.Log("✅ HTTP request to WebSocket endpoint correctly rejected")
	})
}

// TestWebSocketConnectionResolution verifies the specific issue is resolved
func TestWebSocketConnectionResolution(t *testing.T) {
	t.Log("=== WebSocket Connection Issue Resolution Test ===")
	
	// This test specifically addresses the reported issue:
	// "WebSocket connection to 'ws://localhost:8081/ws' failed"
	
	// Create a server setup that matches the actual application
	hub := NewHub(&HubConfig{
		MaxClients:          1000,
		CleanupInterval:     30 * time.Second,
		ClientTimeout:       5 * time.Minute,
		BroadcastBufferSize: 1000,
	})
	go hub.Run()
	defer hub.Stop()

	handlerConfig := &HandlerConfig{
		CheckOrigin: func(r *http.Request) bool {
			return true // Allow all origins for testing
		},
		ReadBufferSize:    1024,
		WriteBufferSize:   1024,
		EnableCompression: true,
		Subprotocols:      []string{"chatport-v1"},
	}

	handler := NewHandler(hub, handlerConfig)

	// Setup routes exactly like the main server
	mux := http.NewServeMux()
	mux.HandleFunc("/ws", handler.ServeHTTP)
	mux.HandleFunc("/api/ws/stats", handler.WebSocketStatsHandler)

	server := httptest.NewServer(mux)
	defer server.Close()

	// Test the exact scenario that was failing
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/ws"
	
	t.Logf("Testing connection to: %s", wsURL)
	
	// Use the same dialer configuration as the client would
	dialer := websocket.Dialer{
		HandshakeTimeout: 10 * time.Second,
		Subprotocols:     []string{"chatport-v1"},
	}

	conn, resp, err := dialer.Dial(wsURL, nil)
	if err != nil {
		t.Fatalf("❌ WebSocket connection failed (this was the original issue): %v", err)
	}
	defer conn.Close()

	t.Logf("✅ WebSocket connection successful! Response: %v", resp.Status)

	// Verify the connection is fully functional
	_, message, err := conn.ReadMessage()
	if err != nil {
		t.Fatalf("Failed to read from WebSocket: %v", err)
	}

	var msg map[string]interface{}
	err = json.Unmarshal(message, &msg)
	if err != nil {
		t.Fatalf("Failed to parse WebSocket message: %v", err)
	}

	t.Logf("✅ Received message type: %v", msg["type"])

	// Verify stats endpoint works
	statsResp, err := http.Get(server.URL + "/api/ws/stats")
	if err != nil {
		t.Fatalf("Failed to get stats: %v", err)
	}
	defer statsResp.Body.Close()

	if statsResp.StatusCode != http.StatusOK {
		t.Fatalf("Stats endpoint returned status %d", statsResp.StatusCode)
	}

	t.Log("✅ WebSocket stats endpoint working")

	t.Log("🎉 ISSUE RESOLVED: WebSocket connection to '/ws' is now working correctly!")
}
