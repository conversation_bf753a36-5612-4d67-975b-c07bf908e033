<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatPort WebSocket Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
        }
        .whatsapp {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
        }
        .status-update {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .metrics {
            background-color: #d1ecf1;
            border-left: 4px solid #17a2b8;
        }
        .health {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }
        .error {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .primary {
            background-color: #007bff;
            color: white;
        }
        .secondary {
            background-color: #6c757d;
            color: white;
        }
        .success {
            background-color: #28a745;
            color: white;
        }
        .danger {
            background-color: #dc3545;
            color: white;
        }
        .info {
            background-color: #17a2b8;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ChatPort WebSocket Client</h1>
        
        <div id="status" class="status disconnected">
            Disconnected
        </div>
        
        <div class="controls">
            <button id="connect" class="primary">Connect</button>
            <button id="disconnect" class="secondary" disabled>Disconnect</button>
            <button id="subscribe-all" class="success" disabled>Subscribe to All</button>
            <button id="subscribe-whatsapp" class="info" disabled>Subscribe to WhatsApp</button>
            <button id="subscribe-metrics" class="info" disabled>Subscribe to Metrics</button>
            <button id="ping" class="info" disabled>Send Ping</button>
            <button id="clear" class="danger">Clear Messages</button>
        </div>

        <div class="controls" style="margin-top: 10px;">
            <input type="text" id="custom-message" placeholder="Enter custom JSON message" style="flex: 1; padding: 8px; border: 1px solid #ccc; border-radius: 4px;" disabled>
            <button id="send-custom" class="primary" disabled>Send Custom</button>
        </div>
        
        <div class="messages" id="messages">
            <div class="message">Welcome! Click "Connect" to start receiving real-time updates from ChatPort.</div>
        </div>
    </div>

    <script>
        let ws = null;
        let clientId = null;

        const statusEl = document.getElementById('status');
        const messagesEl = document.getElementById('messages');
        const connectBtn = document.getElementById('connect');
        const disconnectBtn = document.getElementById('disconnect');
        const subscribeAllBtn = document.getElementById('subscribe-all');
        const subscribeWhatsAppBtn = document.getElementById('subscribe-whatsapp');
        const subscribeMetricsBtn = document.getElementById('subscribe-metrics');
        const pingBtn = document.getElementById('ping');
        const clearBtn = document.getElementById('clear');
        const customMessageInput = document.getElementById('custom-message');
        const sendCustomBtn = document.getElementById('send-custom');

        function updateStatus(connected) {
            if (connected) {
                statusEl.textContent = `Connected (Client ID: ${clientId})`;
                statusEl.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                subscribeAllBtn.disabled = false;
                subscribeWhatsAppBtn.disabled = false;
                subscribeMetricsBtn.disabled = false;
                pingBtn.disabled = false;
                customMessageInput.disabled = false;
                sendCustomBtn.disabled = false;
            } else {
                statusEl.textContent = 'Disconnected';
                statusEl.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                subscribeAllBtn.disabled = true;
                subscribeWhatsAppBtn.disabled = true;
                subscribeMetricsBtn.disabled = true;
                pingBtn.disabled = true;
                customMessageInput.disabled = true;
                sendCustomBtn.disabled = true;
                clientId = null;
            }
        }

        function addMessage(type, content) {
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            messageEl.innerHTML = `<strong>[${timestamp}]</strong> ${content}`;
            
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function connect() {
            const wsUrl = 'ws://localhost:8081/ws';
            ws = new WebSocket(wsUrl);

            ws.onopen = function(event) {
                updateStatus(true);
                addMessage('success', 'Connected to ChatPort WebSocket server');
            };

            ws.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    handleMessage(message);
                } catch (e) {
                    addMessage('error', `Failed to parse message: ${e.message}`);
                    addMessage('error', `Raw message data: ${event.data}`);
                    console.error('WebSocket message parsing error:', e);
                    console.error('Raw message:', event.data);
                }
            };

            ws.onclose = function(event) {
                updateStatus(false);
                addMessage('error', `Connection closed: ${event.reason || 'Unknown reason'}`);
            };

            ws.onerror = function(error) {
                addMessage('error', `WebSocket error: ${error.message || 'Unknown error'}`);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function subscribe(subscription) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'subscribe',
                    timestamp: new Date().toISOString(),
                    data: {
                        action: 'subscribe',
                        subscription: subscription
                    }
                };
                try {
                    ws.send(JSON.stringify(message));
                    addMessage('info', `Subscribed to: ${subscription}`);
                } catch (error) {
                    addMessage('error', `Failed to send subscription: ${error.message}`);
                }
            }
        }

        function sendPing() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'ping',
                    timestamp: new Date().toISOString()
                };
                try {
                    ws.send(JSON.stringify(message));
                    addMessage('info', 'Ping sent');
                } catch (error) {
                    addMessage('error', `Failed to send ping: ${error.message}`);
                }
            }
        }

        function sendCustomMessage() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const messageText = customMessageInput.value.trim();
                if (!messageText) {
                    addMessage('error', 'Please enter a message');
                    return;
                }

                try {
                    // Try to parse as JSON first to validate
                    const parsedMessage = JSON.parse(messageText);

                    // Send the validated JSON
                    ws.send(JSON.stringify(parsedMessage));
                    addMessage('info', `Custom message sent: ${messageText}`);
                    customMessageInput.value = '';
                } catch (error) {
                    addMessage('error', `Invalid JSON: ${error.message}`);
                }
            }
        }

        function handleMessage(message) {
            switch (message.type) {
                case 'welcome':
                    const data = JSON.parse(message.data);
                    clientId = data.client_id;
                    updateStatus(true);
                    addMessage('success', `Welcome! Server version: ${data.version}`);
                    break;

                case 'incoming_whatsapp':
                    const whatsapp = JSON.parse(message.data);
                    addMessage('whatsapp', `WhatsApp from ${whatsapp.from}: ${whatsapp.message}`);
                    break;

                case 'status_update':
                    const status = JSON.parse(message.data);
                    addMessage('status-update', `${status.service} status: ${status.status} - ${status.details}`);
                    break;

                case 'metrics_update':
                    const metrics = JSON.parse(message.data);
                    addMessage('metrics', `Metrics - Messages: ${metrics.messages_sent}/${metrics.messages_received}, HTTP: ${metrics.http_requests}, Success Rate: ${metrics.message_success_rate.toFixed(1)}%`);
                    break;

                case 'health_update':
                    const health = JSON.parse(message.data);
                    addMessage('health', `Health status changed: ${health.previous_status} → ${health.status}`);
                    break;

                case 'error':
                    const error = JSON.parse(message.data);
                    addMessage('error', `Error ${error.code}: ${error.message}`);
                    break;

                case 'ping':
                    // Respond with pong
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({
                            type: 'pong',
                            timestamp: new Date().toISOString()
                        }));
                    }
                    break;

                default:
                    addMessage('info', `Unknown message type: ${message.type}`);
            }
        }

        // Event listeners
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        subscribeAllBtn.addEventListener('click', () => subscribe('all'));
        subscribeWhatsAppBtn.addEventListener('click', () => subscribe('whatsapp'));
        subscribeMetricsBtn.addEventListener('click', () => subscribe('metrics'));
        pingBtn.addEventListener('click', sendPing);
        sendCustomBtn.addEventListener('click', sendCustomMessage);
        customMessageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendCustomMessage();
            }
        });
        clearBtn.addEventListener('click', () => {
            messagesEl.innerHTML = '';
        });

        // Initialize
        updateStatus(false);
    </script>
</body>
</html>
