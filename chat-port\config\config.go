package config

import (
	"fmt"
	"log"
	"os"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/joho/godotenv"
)

// Config holds all configuration values
type Config struct {
	// Server configuration
	Server ServerConfig `json:"server"`

	// WhatsApp configuration
	WhatsApp WhatsAppConfig `json:"whatsapp"`

	// AI Service configuration
	AIService AIServiceConfig `json:"ai_service"`

	// Logging configuration
	Logging LoggingConfig `json:"logging"`
}

// ServerConfig holds HTTP server configuration
type ServerConfig struct {
	Port         string        `json:"port"`
	ReadTimeout  time.Duration `json:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout"`
	IdleTimeout  time.Duration `json:"idle_timeout"`
}

// WhatsAppConfig holds WhatsApp client configuration
type WhatsAppConfig struct {
	DBPath         string        `json:"db_path"`
	QRTimeout      time.Duration `json:"qr_timeout"`
	ReconnectDelay time.Duration `json:"reconnect_delay"`
	MaxReconnects  int           `json:"max_reconnects"`
}

// AIServiceConfig holds AI service configuration
type AIServiceConfig struct {
	URL           string        `json:"url"`
	Timeout       time.Duration `json:"timeout"`
	RetryAttempts int           `json:"retry_attempts"`
	RetryDelay    time.Duration `json:"retry_delay"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level  string `json:"level"`
	Format string `json:"format"`
}

var globalConfig *Config

// LoadEnv loads environment variables from .env file
func LoadEnv() {
	err := godotenv.Load()
	if err != nil {
		log.Println("No .env file found, using system environment variables")
	}
}

// Load loads and validates the configuration
func Load() (*Config, error) {
	LoadEnv()

	config := &Config{
		Server: ServerConfig{
			Port:         GetEnv("SERVER_PORT", "8081"),
			ReadTimeout:  GetDurationEnv("SERVER_READ_TIMEOUT", 15*time.Second),
			WriteTimeout: GetDurationEnv("SERVER_WRITE_TIMEOUT", 15*time.Second),
			IdleTimeout:  GetDurationEnv("SERVER_IDLE_TIMEOUT", 60*time.Second),
		},
		WhatsApp: WhatsAppConfig{
			DBPath:         GetEnv("WHATSAPP_DB_PATH", "file:session.db?_foreign_keys=on"),
			QRTimeout:      GetDurationEnv("WHATSAPP_QR_TIMEOUT", 2*time.Minute),
			ReconnectDelay: GetDurationEnv("WHATSAPP_RECONNECT_DELAY", 5*time.Second),
			MaxReconnects:  GetIntEnv("WHATSAPP_MAX_RECONNECTS", 5),
		},
		AIService: AIServiceConfig{
			URL:           GetEnv("AI_SERVICE_URL", "http://localhost:8080"),
			Timeout:       GetDurationEnv("AI_SERVICE_TIMEOUT", 30*time.Second),
			RetryAttempts: GetIntEnv("AI_SERVICE_RETRY_ATTEMPTS", 3),
			RetryDelay:    GetDurationEnv("AI_SERVICE_RETRY_DELAY", 1*time.Second),
		},
		Logging: LoggingConfig{
			Level:  GetEnv("LOG_LEVEL", "info"),
			Format: GetEnv("LOG_FORMAT", "text"),
		},
	}

	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	globalConfig = config
	return config, nil
}

// Get returns the global configuration instance
func Get() *Config {
	if globalConfig == nil {
		config, err := Load()
		if err != nil {
			log.Fatalf("Failed to load configuration: %v", err)
		}
		return config
	}
	return globalConfig
}

// GetEnv gets an environment variable with a fallback value
func GetEnv(key string, fallback string) string {
	if value, ok := os.LookupEnv(key); ok {
		return value
	}
	return fallback
}

// GetIntEnv gets an integer environment variable with a fallback value
func GetIntEnv(key string, fallback int) int {
	if value, ok := os.LookupEnv(key); ok {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
		log.Printf("Warning: Invalid integer value for %s: %s, using fallback: %d", key, value, fallback)
	}
	return fallback
}

// GetDurationEnv gets a duration environment variable with a fallback value
func GetDurationEnv(key string, fallback time.Duration) time.Duration {
	if value, ok := os.LookupEnv(key); ok {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
		log.Printf("Warning: Invalid duration value for %s: %s, using fallback: %v", key, value, fallback)
	}
	return fallback
}

// GetBoolEnv gets a boolean environment variable with a fallback value
func GetBoolEnv(key string, fallback bool) bool {
	if value, ok := os.LookupEnv(key); ok {
		switch strings.ToLower(value) {
		case "true", "1", "yes", "on":
			return true
		case "false", "0", "no", "off":
			return false
		default:
			log.Printf("Warning: Invalid boolean value for %s: %s, using fallback: %v", key, value, fallback)
		}
	}
	return fallback
}

// Validate validates the configuration values
func (c *Config) Validate() error {
	// Validate server configuration
	if c.Server.Port == "" {
		return fmt.Errorf("server port cannot be empty")
	}

	if c.Server.ReadTimeout <= 0 {
		return fmt.Errorf("server read timeout must be positive")
	}

	if c.Server.WriteTimeout <= 0 {
		return fmt.Errorf("server write timeout must be positive")
	}

	if c.Server.IdleTimeout <= 0 {
		return fmt.Errorf("server idle timeout must be positive")
	}

	// Validate WhatsApp configuration
	if c.WhatsApp.DBPath == "" {
		return fmt.Errorf("WhatsApp database path cannot be empty")
	}

	if c.WhatsApp.QRTimeout <= 0 {
		return fmt.Errorf("WhatsApp QR timeout must be positive")
	}

	if c.WhatsApp.ReconnectDelay <= 0 {
		return fmt.Errorf("WhatsApp reconnect delay must be positive")
	}

	if c.WhatsApp.MaxReconnects < 0 {
		return fmt.Errorf("WhatsApp max reconnects cannot be negative")
	}

	// Validate AI service configuration
	if c.AIService.URL == "" {
		return fmt.Errorf("AI service URL cannot be empty")
	}

	if c.AIService.Timeout <= 0 {
		return fmt.Errorf("AI service timeout must be positive")
	}

	if c.AIService.RetryAttempts < 0 {
		return fmt.Errorf("AI service retry attempts cannot be negative")
	}

	if c.AIService.RetryDelay < 0 {
		return fmt.Errorf("AI service retry delay cannot be negative")
	}

	// Validate logging configuration
	validLogLevels := []string{"debug", "info", "warn", "error"}
	validLevel := slices.Contains(validLogLevels, strings.ToLower(c.Logging.Level))
	if !validLevel {
		return fmt.Errorf("invalid log level: %s, must be one of: %v", c.Logging.Level, validLogLevels)
	}

	validLogFormats := []string{"text", "json"}
	validFormat := slices.Contains(validLogFormats, strings.ToLower(c.Logging.Format))
	if !validFormat {
		return fmt.Errorf("invalid log format: %s, must be one of: %v", c.Logging.Format, validLogFormats)
	}

	return nil
}
