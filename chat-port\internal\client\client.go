package client

import (
	"context"
	"log"

	"go.mau.fi/whatsmeow"
	waE2E "go.mau.fi/whatsmeow/proto/waE2E"
	"go.mau.fi/whatsmeow/store/sqlstore"
	"go.mau.fi/whatsmeow/types"
)

var Client *whatsmeow.Client

func InitClient() {
	container, err := sqlstore.New("sqlite3", "file:session.db?_foreign_keys=on", nil)
	if err != nil {
		log.Fatalf("Failed to create DB: %v", err)
	}
	deviceStore, err := container.GetFirstDevice()
	if err != nil {
		log.Fatalf("Failed to get device: %v", err)
	}

	Client = whatsmeow.NewClient(deviceStore, nil)

	if Client.Store.ID == nil {
		qrChan, _ := Client.GetQRChannel(context.Background())
		err = Client.Connect()
		if err != nil {
			log.Fatalf("Failed to connect: %v", err)
		}

		for evt := range qrChan {
			if evt.Event == "code" {
				log.Printf("Scan QR Code: %s", evt.Code)
			} else if evt.Event == "success" {
				log.Println("Logged in successfully")
				break
			} else if evt.Event == "timeout" || evt.Event == "error" {
				log.Fatalf("QR error: %v", evt.Event)
			}
		}
	} else {
		err = Client.Connect()
		if err != nil {
			log.Fatalf("Reconnect failed: %v", err)
		}
	}

	Client.AddEventHandler(func(evt interface{}) {
		switch v := evt.(type) {
		case *waE2E.Message:
			go handleIncomingMessage(v)
		}
	})
}

func handleIncomingMessage(msg *waE2E.Message) {
	jid := msg.Info.Sender.User
	text := msg.Message.GetConversation()

	log.Printf("Received from %s: %s", jid, text)

	// Forward to Rust AI service (you can replace the URL)
	// POST: { "number": jid, "message": text }
	// Handle errors silently or log.
}

func SendMessage(number string, message string) error {
	jid := types.NewJID(number, "s.whatsapp.net")
	_, err := Client.SendMessage(context.Background(), jid, &waE2E.Message{
		Conversation: &message,
	})
	return err
}
