package websocket

import (
	"context"
	"errors"
	"sync"
	"time"

	"chatport-go/internal/logger"
	"chatport-go/internal/monitoring"
)

var (
	ErrHubClosed   = errors.New("hub is closed")
	ErrClientSlow  = errors.New("client is too slow")
	ErrMaxClients  = errors.New("maximum number of clients reached")
)

// HubConfig holds configuration for the WebSocket hub
type HubConfig struct {
	MaxClients          int           `json:"max_clients"`
	CleanupInterval     time.Duration `json:"cleanup_interval"`
	ClientTimeout       time.Duration `json:"client_timeout"`
	BroadcastBufferSize int           `json:"broadcast_buffer_size"`
}

// DefaultHubConfig returns default hub configuration
func DefaultHubConfig() *HubConfig {
	return &HubConfig{
		MaxClients:          1000,
		CleanupInterval:     30 * time.Second,
		ClientTimeout:       5 * time.Minute,
		BroadcastBufferSize: 1000,
	}
}

// Hu<PERSON> maintains the set of active clients and broadcasts messages to the clients
type Hub struct {
	// Registered clients
	clients map[*Client]bool

	// Inbound messages from the clients
	broadcast chan *BroadcastMessage

	// Register requests from the clients
	register chan *Client

	// Unregister requests from clients
	unregister chan *Client

	// Hub configuration
	config *HubConfig

	// Context for cancellation
	ctx context.Context
	cancel context.CancelFunc

	// Mutex for thread-safe access to clients
	clientsMu sync.RWMutex

	// Statistics
	stats *HubStats
	statsMu sync.RWMutex

	// Cleanup ticker
	cleanupTicker *time.Ticker
}

// BroadcastMessage represents a message to be broadcast to clients
type BroadcastMessage struct {
	Message        *Message
	SubscriptionType SubscriptionType
	ClientFilter   func(*Client) bool // Optional filter function
}

// HubStats holds hub statistics
type HubStats struct {
	TotalConnections    int64     `json:"total_connections"`
	CurrentConnections  int       `json:"current_connections"`
	MessagesSent        int64     `json:"messages_sent"`
	MessagesDropped     int64     `json:"messages_dropped"`
	ClientsTimedOut     int64     `json:"clients_timed_out"`
	LastActivity        time.Time `json:"last_activity"`
	StartTime           time.Time `json:"start_time"`
}

// NewHub creates a new WebSocket hub
func NewHub(config *HubConfig) *Hub {
	if config == nil {
		config = DefaultHubConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	hub := &Hub{
		clients:       make(map[*Client]bool),
		broadcast:     make(chan *BroadcastMessage, config.BroadcastBufferSize),
		register:      make(chan *Client),
		unregister:    make(chan *Client),
		config:        config,
		ctx:           ctx,
		cancel:        cancel,
		stats: &HubStats{
			StartTime: time.Now(),
		},
		cleanupTicker: time.NewTicker(config.CleanupInterval),
	}

	return hub
}

// Run starts the hub's main loop
func (h *Hub) Run() {
	defer func() {
		h.cleanupTicker.Stop()
		h.closeAllClients()
	}()

	logger.Info("WebSocket hub started")

	for {
		select {
		case <-h.ctx.Done():
			logger.Info("WebSocket hub shutting down")
			return

		case client := <-h.register:
			h.registerClient(client)

		case client := <-h.unregister:
			h.unregisterClient(client)

		case broadcastMsg := <-h.broadcast:
			h.broadcastMessage(broadcastMsg)

		case <-h.cleanupTicker.C:
			h.cleanupInactiveClients()
		}
	}
}

// Stop stops the hub
func (h *Hub) Stop() {
	h.cancel()
}

// RegisterClient registers a new client
func (h *Hub) RegisterClient(client *Client) error {
	h.clientsMu.RLock()
	currentCount := len(h.clients)
	h.clientsMu.RUnlock()

	if currentCount >= h.config.MaxClients {
		return ErrMaxClients
	}

	select {
	case h.register <- client:
		return nil
	case <-h.ctx.Done():
		return ErrHubClosed
	}
}

// registerClient handles client registration
func (h *Hub) registerClient(client *Client) {
	h.clientsMu.Lock()
	h.clients[client] = true
	h.clientsMu.Unlock()

	h.statsMu.Lock()
	h.stats.TotalConnections++
	h.stats.CurrentConnections = len(h.clients)
	h.stats.LastActivity = time.Now()
	h.statsMu.Unlock()

	logger.Infof("Client %s registered, total clients: %d", client.ID(), len(h.clients))
	monitoring.IncrementHTTPRequests() // Track WebSocket connections as requests
}

// unregisterClient handles client unregistration
func (h *Hub) unregisterClient(client *Client) {
	h.clientsMu.Lock()
	if _, ok := h.clients[client]; ok {
		delete(h.clients, client)
		client.Close()
	}
	h.clientsMu.Unlock()

	h.statsMu.Lock()
	h.stats.CurrentConnections = len(h.clients)
	h.statsMu.Unlock()

	logger.Infof("Client %s unregistered, total clients: %d", client.ID(), len(h.clients))
}

// BroadcastToAll broadcasts a message to all connected clients
func (h *Hub) BroadcastToAll(message *Message) {
	h.Broadcast(message, SubscriptionAll, nil)
}

// BroadcastToSubscription broadcasts a message to clients subscribed to a specific type
func (h *Hub) BroadcastToSubscription(message *Message, subType SubscriptionType) {
	h.Broadcast(message, subType, nil)
}

// Broadcast broadcasts a message to clients with optional filtering
func (h *Hub) Broadcast(message *Message, subType SubscriptionType, filter func(*Client) bool) {
	broadcastMsg := &BroadcastMessage{
		Message:          message,
		SubscriptionType: subType,
		ClientFilter:     filter,
	}

	select {
	case h.broadcast <- broadcastMsg:
	case <-h.ctx.Done():
		logger.Warn("Hub is closed, cannot broadcast message")
	default:
		h.statsMu.Lock()
		h.stats.MessagesDropped++
		h.statsMu.Unlock()
		logger.Warn("Broadcast channel is full, dropping message")
	}
}

// broadcastMessage handles the actual broadcasting
func (h *Hub) broadcastMessage(broadcastMsg *BroadcastMessage) {
	h.clientsMu.RLock()
	clients := make([]*Client, 0, len(h.clients))
	for client := range h.clients {
		clients = append(clients, client)
	}
	h.clientsMu.RUnlock()

	sentCount := 0
	droppedCount := 0

	for _, client := range clients {
		// Check subscription
		if !client.IsSubscribed(broadcastMsg.SubscriptionType) {
			continue
		}

		// Apply client filter if provided
		if broadcastMsg.ClientFilter != nil && !broadcastMsg.ClientFilter(client) {
			continue
		}

		// Send message to client
		err := client.SendMessage(broadcastMsg.Message)
		if err != nil {
			if err == ErrClientSlow {
				droppedCount++
			} else {
				logger.Errorf("Failed to send message to client %s: %v", client.ID(), err)
			}
		} else {
			sentCount++
		}
	}

	h.statsMu.Lock()
	h.stats.MessagesSent += int64(sentCount)
	h.stats.MessagesDropped += int64(droppedCount)
	h.stats.LastActivity = time.Now()
	h.statsMu.Unlock()

	if sentCount > 0 || droppedCount > 0 {
		logger.Debugf("Broadcast complete: sent=%d, dropped=%d, subscription=%s", 
			sentCount, droppedCount, broadcastMsg.SubscriptionType)
	}
}

// cleanupInactiveClients removes clients that have been inactive for too long
func (h *Hub) cleanupInactiveClients() {
	now := time.Now()
	timeout := h.config.ClientTimeout

	h.clientsMu.RLock()
	inactiveClients := make([]*Client, 0)
	for client := range h.clients {
		if now.Sub(client.LastActivity()) > timeout {
			inactiveClients = append(inactiveClients, client)
		}
	}
	h.clientsMu.RUnlock()

	for _, client := range inactiveClients {
		logger.Infof("Removing inactive client %s (last activity: %v)", 
			client.ID(), client.LastActivity())
		h.unregister <- client
		
		h.statsMu.Lock()
		h.stats.ClientsTimedOut++
		h.statsMu.Unlock()
	}
}

// closeAllClients closes all connected clients
func (h *Hub) closeAllClients() {
	h.clientsMu.Lock()
	defer h.clientsMu.Unlock()

	for client := range h.clients {
		client.Close()
	}
	h.clients = make(map[*Client]bool)

	h.statsMu.Lock()
	h.stats.CurrentConnections = 0
	h.statsMu.Unlock()

	logger.Info("All WebSocket clients closed")
}

// GetStats returns hub statistics
func (h *Hub) GetStats() *HubStats {
	h.statsMu.RLock()
	defer h.statsMu.RUnlock()

	// Return a copy to avoid race conditions
	return &HubStats{
		TotalConnections:   h.stats.TotalConnections,
		CurrentConnections: h.stats.CurrentConnections,
		MessagesSent:       h.stats.MessagesSent,
		MessagesDropped:    h.stats.MessagesDropped,
		ClientsTimedOut:    h.stats.ClientsTimedOut,
		LastActivity:       h.stats.LastActivity,
		StartTime:          h.stats.StartTime,
	}
}

// GetClientCount returns the current number of connected clients
func (h *Hub) GetClientCount() int {
	h.clientsMu.RLock()
	defer h.clientsMu.RUnlock()
	return len(h.clients)
}

// GetClients returns information about all connected clients
func (h *Hub) GetClients() []map[string]interface{} {
	h.clientsMu.RLock()
	defer h.clientsMu.RUnlock()

	clients := make([]map[string]interface{}, 0, len(h.clients))
	for client := range h.clients {
		clients = append(clients, client.Stats())
	}

	return clients
}

// GetClientByID returns a client by ID
func (h *Hub) GetClientByID(id string) *Client {
	h.clientsMu.RLock()
	defer h.clientsMu.RUnlock()

	for client := range h.clients {
		if client.ID() == id {
			return client
		}
	}

	return nil
}
