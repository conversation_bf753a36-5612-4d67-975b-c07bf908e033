package server

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"chatport-go/config"
	"chatport-go/internal/handlers"
	"chatport-go/internal/monitoring"
	websocketPkg "chatport-go/internal/websocket"

	"github.com/gorilla/mux"
)

// Server represents the HTTP server
type Server struct {
	httpServer *http.Server
	router     *mux.Router
	port       string

	// WebSocket components
	wsHub         *websocketPkg.Hub
	wsHandler     *websocketPkg.Handler
	wsBroadcaster *websocketPkg.Broadcaster
}

// New creates a new HTTP server instance
func New() *Server {
	config.LoadEnv()
	cfg := config.Get()

	port := config.GetEnv("SERVER_PORT", "8081")
	router := mux.NewRouter()

	server := &Server{
		router: router,
		port:   port,
	}

	// Initialize WebSocket components if enabled
	if cfg.WebSocket.Enabled {
		server.initWebSocket(cfg)
	}

	server.setupRoutes()
	server.setupMiddleware()

	server.httpServer = &http.Server{
		Addr:         ":" + port,
		Handler:      router,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	return server
}

// initWebSocket initializes WebSocket components
func (s *Server) initWebSocket(cfg *config.Config) {
	// Create hub configuration
	hubConfig := &websocketPkg.HubConfig{
		MaxClients:          cfg.WebSocket.MaxClients,
		CleanupInterval:     cfg.WebSocket.CleanupInterval,
		ClientTimeout:       cfg.WebSocket.ClientTimeout,
		BroadcastBufferSize: cfg.WebSocket.BroadcastBufferSize,
	}

	// Create and start hub
	s.wsHub = websocketPkg.NewHub(hubConfig)
	go s.wsHub.Run()

	// Create handler configuration
	handlerConfig := &websocketPkg.HandlerConfig{
		CheckOrigin: func(r *http.Request) bool {
			origin := r.Header.Get("Origin")
			if len(cfg.WebSocket.AllowedOrigins) == 0 {
				return true // Allow all if no origins specified
			}
			for _, allowed := range cfg.WebSocket.AllowedOrigins {
				if allowed == "*" || allowed == origin {
					return true
				}
			}
			return false
		},
		ReadBufferSize:    cfg.WebSocket.ReadBufferSize,
		WriteBufferSize:   cfg.WebSocket.WriteBufferSize,
		EnableCompression: cfg.WebSocket.EnableCompression,
		Subprotocols:      []string{"chatport-v1"},
		Authenticate:      nil, // TODO: Add authentication if enabled
	}

	// Create WebSocket handler
	s.wsHandler = websocketPkg.NewHandler(s.wsHub, handlerConfig)

	// Create broadcaster configuration
	broadcasterConfig := &websocketPkg.BroadcasterConfig{
		MetricsInterval:        cfg.WebSocket.MetricsInterval,
		BufferSize:             100,
		EnableMetricsBroadcast: true,
		EnableHealthBroadcast:  true,
	}

	// Create and start broadcaster
	s.wsBroadcaster = websocketPkg.NewBroadcaster(s.wsHub, broadcasterConfig)
	go s.wsBroadcaster.Run()

	// Initialize global broadcaster
	websocketPkg.InitGlobalBroadcaster(s.wsHub, broadcasterConfig)
}

// setupRoutes configures all the API routes
func (s *Server) setupRoutes() {
	// API routes
	api := s.router.PathPrefix("/api").Subrouter()

	// WhatsApp message sending endpoint
	api.HandleFunc("/send", handlers.SendHandler).Methods("POST")

	// Health check endpoint
	api.HandleFunc("/health", handlers.HealthHandler).Methods("GET")

	// Status endpoint
	api.HandleFunc("/status", handlers.StatusHandler).Methods("GET")

	// Metrics endpoint
	api.HandleFunc("/metrics", handlers.MetricsHandler).Methods("GET")

	// WebSocket endpoints (if WebSocket is enabled)
	if s.wsHandler != nil {
		// WebSocket connection endpoint
		s.router.HandleFunc("/ws", s.wsHandler.ServeHTTP).Methods("GET")

		// WebSocket statistics endpoint
		api.HandleFunc("/ws/stats", s.wsHandler.WebSocketStatsHandler).Methods("GET")
	}
}

// setupMiddleware configures middleware for the server
func (s *Server) setupMiddleware() {
	// Monitoring middleware (should be first to capture all requests)
	s.router.Use(monitoringMiddleware)

	// Logging middleware
	s.router.Use(loggingMiddleware)

	// CORS middleware
	s.router.Use(corsMiddleware)

	// Content-Type middleware
	s.router.Use(contentTypeMiddleware)
}

// Start starts the HTTP server
func (s *Server) Start() error {
	log.Printf("Starting HTTP server on port %s", s.port)

	if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		return fmt.Errorf("failed to start server: %w", err)
	}

	return nil
}

// Stop gracefully stops the HTTP server
func (s *Server) Stop(ctx context.Context) error {
	log.Println("Shutting down HTTP server...")

	// Stop WebSocket components if they exist
	if s.wsBroadcaster != nil {
		s.wsBroadcaster.Stop()
	}
	if s.wsHub != nil {
		s.wsHub.Stop()
	}

	return s.httpServer.Shutdown(ctx)
}

// GetPort returns the server port
func (s *Server) GetPort() string {
	return s.port
}

// Middleware functions

// monitoringMiddleware tracks HTTP request metrics
func monitoringMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		monitoring.IncrementHTTPRequests()

		// Create a response writer wrapper to capture status code
		wrapper := &responseWriter{ResponseWriter: w, statusCode: http.StatusOK}

		next.ServeHTTP(wrapper, r)

		// Track errors (4xx and 5xx status codes)
		if wrapper.statusCode >= 400 {
			monitoring.IncrementHTTPErrors()
		}
	})
}

// loggingMiddleware logs HTTP requests
func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		// Create a response writer wrapper to capture status code
		wrapper := &responseWriter{ResponseWriter: w, statusCode: http.StatusOK}

		next.ServeHTTP(wrapper, r)

		duration := time.Since(start)
		log.Printf("%s %s %d %v", r.Method, r.URL.Path, wrapper.statusCode, duration)
	})
}

// corsMiddleware handles CORS headers
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// contentTypeMiddleware sets default content type for API responses
func contentTypeMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path[:4] == "/api" {
			w.Header().Set("Content-Type", "application/json")
		}
		next.ServeHTTP(w, r)
	})
}

// responseWriter wraps http.ResponseWriter to capture status code
type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}
