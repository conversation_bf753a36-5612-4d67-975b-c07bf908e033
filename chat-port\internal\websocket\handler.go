package websocket

import (
	"crypto/rand"
	"encoding/hex"
	"net/http"
	"strings"
	"time"

	"chatport-go/internal/logger"
	"chatport-go/internal/monitoring"

	"github.com/gorilla/websocket"
)

// HandlerConfig holds configuration for the WebSocket handler
type HandlerConfig struct {
	// CheckOrigin function to validate origins
	CheckOrigin func(r *http.Request) bool

	// ReadBufferSize and WriteBufferSize specify I/O buffer sizes in bytes
	ReadBufferSize  int
	WriteBufferSize int

	// EnableCompression specifies if the server should attempt to negotiate
	// per message compression (RFC 7692)
	EnableCompression bool

	// Subprotocols specifies the supported WebSocket subprotocols
	Subprotocols []string

	// Authentication function (optional)
	Authenticate func(r *http.Request) (bool, string, error)
}

// DefaultHandlerConfig returns default handler configuration
func DefaultHandlerConfig() *HandlerConfig {
	return &HandlerConfig{
		CheckOrigin: func(r *http.Request) bool {
			// Allow all origins by default - should be restricted in production
			return true
		},
		ReadBufferSize:    1024,
		WriteBufferSize:   1024,
		EnableCompression: true,
		Subprotocols:      []string{"chatport-v1"},
		Authenticate:      nil, // No authentication by default
	}
}

// Handler handles WebSocket connections
type Handler struct {
	hub      *Hub
	upgrader websocket.Upgrader
	config   *HandlerConfig
}

// NewHandler creates a new WebSocket handler
func NewHandler(hub *Hub, config *HandlerConfig) *Handler {
	if config == nil {
		config = DefaultHandlerConfig()
	}

	upgrader := websocket.Upgrader{
		ReadBufferSize:    config.ReadBufferSize,
		WriteBufferSize:   config.WriteBufferSize,
		EnableCompression: config.EnableCompression,
		Subprotocols:      config.Subprotocols,
		CheckOrigin:       config.CheckOrigin,
	}

	return &Handler{
		hub:      hub,
		upgrader: upgrader,
		config:   config,
	}
}

// ServeHTTP handles WebSocket upgrade requests
func (h *Handler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// Track the request
	monitoring.IncrementHTTPRequests()

	// Validate request method
	if r.Method != http.MethodGet {
		monitoring.IncrementHTTPErrors()
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Authenticate if authentication is configured
	if h.config.Authenticate != nil {
		authenticated, userID, err := h.config.Authenticate(r)
		if err != nil {
			logger.Errorf("Authentication error: %v", err)
			monitoring.IncrementHTTPErrors()
			http.Error(w, "Authentication error", http.StatusInternalServerError)
			return
		}
		if !authenticated {
			monitoring.IncrementHTTPErrors()
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}
		// Store user ID in request context for later use
		r.Header.Set("X-User-ID", userID)
	}

	// Upgrade the HTTP connection to WebSocket
	conn, err := h.upgrader.Upgrade(w, r, nil)
	if err != nil {
		logger.Errorf("Failed to upgrade WebSocket connection: %v", err)
		monitoring.IncrementHTTPErrors()
		return
	}

	// Generate client ID
	clientID := h.generateClientID(r)

	// Create new client
	client := NewClient(conn, h.hub, clientID, r)

	// Register client with hub
	err = h.hub.RegisterClient(client)
	if err != nil {
		logger.Errorf("Failed to register client %s: %v", clientID, err)
		client.Close()

		// Send close message with reason
		closeCode := websocket.CloseInternalServerErr
		if err == ErrMaxClients {
			closeCode = websocket.CloseTryAgainLater
		}
		conn.WriteMessage(websocket.CloseMessage,
			websocket.FormatCloseMessage(closeCode, err.Error()))
		conn.Close()
		return
	}

	// Send welcome message
	h.sendWelcomeMessage(client)

	// Start client pumps
	client.Run()

	logger.Infof("WebSocket client %s connected from %s", clientID, client.RemoteAddr())
}

// generateClientID generates a unique client ID
func (h *Handler) generateClientID(r *http.Request) string {
	// Use user ID from authentication if available
	if userID := r.Header.Get("X-User-ID"); userID != "" {
		return userID + "-" + h.generateRandomID()
	}

	// Generate random ID
	return "client-" + h.generateRandomID()
}

// generateRandomID generates a random ID
func (h *Handler) generateRandomID() string {
	bytes := make([]byte, 8)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// sendWelcomeMessage sends a welcome message to newly connected clients
func (h *Handler) sendWelcomeMessage(client *Client) {
	welcomeData := map[string]interface{}{
		"client_id":    client.ID(),
		"server_time":  time.Now(),
		"version":      "1.0.0",
		"capabilities": []string{"whatsapp", "status", "metrics", "health"},
		"subscriptions": map[string]string{
			"whatsapp": "Receive incoming WhatsApp messages",
			"status":   "Receive service status updates",
			"metrics":  "Receive real-time metrics",
			"health":   "Receive health status changes",
			"all":      "Receive all message types",
		},
	}

	welcomeMsg, err := NewMessage("welcome", welcomeData)
	if err != nil {
		logger.Errorf("Failed to create welcome message for client %s: %v", client.ID(), err)
		return
	}

	err = client.SendMessage(welcomeMsg)
	if err != nil {
		logger.Errorf("Failed to send welcome message to client %s: %v", client.ID(), err)
	}
}

// WebSocketStatsHandler returns WebSocket statistics
func (h *Handler) WebSocketStatsHandler(w http.ResponseWriter, r *http.Request) {
	monitoring.IncrementHTTPRequests()

	stats := h.hub.GetStats()
	clients := h.hub.GetClients()

	response := map[string]interface{}{
		"hub_stats": stats,
		"clients":   clients,
		"config": map[string]interface{}{
			"max_clients":           h.hub.config.MaxClients,
			"cleanup_interval":      h.hub.config.CleanupInterval.String(),
			"client_timeout":        h.hub.config.ClientTimeout.String(),
			"broadcast_buffer_size": h.hub.config.BroadcastBufferSize,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	// Use encoding/json to write the response
	encoder := json.NewEncoder(w)
	if err := encoder.Encode(response); err != nil {
		logger.Errorf("Failed to write WebSocket stats response: %v", err)
		monitoring.IncrementHTTPErrors()
	}
}

// writeJSON writes JSON response (helper function)
func writeJSON(w http.ResponseWriter, data interface{}) error {
	// This would typically use the same JSON encoder as other handlers
	// For now, we'll use a simple implementation
	w.Header().Set("Content-Type", "application/json")
	// Implementation would go here - using encoding/json for simplicity
	return nil
}

// Middleware functions

// CORSMiddleware handles CORS for WebSocket connections
func CORSMiddleware(allowedOrigins []string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			origin := r.Header.Get("Origin")

			// Check if origin is allowed
			allowed := false
			for _, allowedOrigin := range allowedOrigins {
				if allowedOrigin == "*" || allowedOrigin == origin {
					allowed = true
					break
				}
			}

			if allowed {
				w.Header().Set("Access-Control-Allow-Origin", origin)
			}

			w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, Sec-WebSocket-Protocol")
			w.Header().Set("Access-Control-Allow-Credentials", "true")

			if r.Method == "OPTIONS" {
				w.WriteHeader(http.StatusOK)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// AuthMiddleware provides authentication for WebSocket connections
func AuthMiddleware(authFunc func(r *http.Request) (bool, string, error)) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if authFunc != nil {
				authenticated, userID, err := authFunc(r)
				if err != nil {
					logger.Errorf("Authentication error: %v", err)
					http.Error(w, "Authentication error", http.StatusInternalServerError)
					return
				}
				if !authenticated {
					http.Error(w, "Unauthorized", http.StatusUnauthorized)
					return
				}
				r.Header.Set("X-User-ID", userID)
			}

			next.ServeHTTP(w, r)
		})
	}
}

// LoggingMiddleware logs WebSocket connection attempts
func LoggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		// Check if this is a WebSocket upgrade request
		if strings.ToLower(r.Header.Get("Upgrade")) == "websocket" {
			logger.Infof("WebSocket connection attempt from %s (User-Agent: %s)",
				r.RemoteAddr, r.Header.Get("User-Agent"))
		}

		next.ServeHTTP(w, r)

		duration := time.Since(start)
		logger.Debugf("WebSocket request completed in %v", duration)
	})
}

// RateLimitMiddleware provides rate limiting for WebSocket connections
func RateLimitMiddleware(maxConnections int, timeWindow time.Duration) func(http.Handler) http.Handler {
	// This is a simplified rate limiter - in production you'd want a more sophisticated implementation
	connections := make(map[string][]time.Time)

	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			clientIP := r.RemoteAddr
			if forwarded := r.Header.Get("X-Forwarded-For"); forwarded != "" {
				clientIP = strings.Split(forwarded, ",")[0]
			}

			now := time.Now()

			// Clean old entries
			if times, exists := connections[clientIP]; exists {
				var validTimes []time.Time
				for _, t := range times {
					if now.Sub(t) < timeWindow {
						validTimes = append(validTimes, t)
					}
				}
				connections[clientIP] = validTimes
			}

			// Check rate limit
			if len(connections[clientIP]) >= maxConnections {
				http.Error(w, "Rate limit exceeded", http.StatusTooManyRequests)
				return
			}

			// Add current request
			connections[clientIP] = append(connections[clientIP], now)

			next.ServeHTTP(w, r)
		})
	}
}
