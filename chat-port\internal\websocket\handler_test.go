package websocket

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gorilla/websocket"
)

func TestNewHandler(t *testing.T) {
	hub := NewHub(DefaultHubConfig())
	config := DefaultHandlerConfig()

	handler := NewHandler(hub, config)

	if handler == nil {
		t.Fatal("Expected handler to be created")
	}

	if handler.hub != hub {
		t.Error("Expected handler to reference the provided hub")
	}

	if handler.config != config {
		t.Error("Expected handler to reference the provided config")
	}
}

func TestNewHandler_DefaultConfig(t *testing.T) {
	hub := NewHub(DefaultHubConfig())
	handler := NewHandler(hub, nil)

	if handler == nil {
		t.Fatal("Expected handler to be created with default config")
	}

	if handler.config == nil {
		t.Error("Expected handler to have default config")
	}
}

func TestDefaultHandlerConfig(t *testing.T) {
	config := DefaultHandlerConfig()

	if config.CheckOrigin == nil {
		t.Error("Expected CheckOrigin function to be set")
	}

	if config.ReadBufferSize <= 0 {
		t.Error("Expected positive ReadBufferSize")
	}

	if config.WriteBufferSize <= 0 {
		t.Error("Expected positive WriteBufferSize")
	}

	if len(config.Subprotocols) == 0 {
		t.Error("Expected at least one subprotocol")
	}

	// Test CheckOrigin function (should allow all by default)
	req := httptest.NewRequest("GET", "/ws", nil)
	req.Header.Set("Origin", "http://example.com")

	if !config.CheckOrigin(req) {
		t.Error("Expected default CheckOrigin to allow all origins")
	}
}

func TestHandler_ServeHTTP_MethodNotAllowed(t *testing.T) {
	hub := NewHub(DefaultHubConfig())
	handler := NewHandler(hub, nil)

	// Test POST request (should be rejected)
	req := httptest.NewRequest("POST", "/ws", nil)
	rr := httptest.NewRecorder()

	handler.ServeHTTP(rr, req)

	if rr.Code != http.StatusMethodNotAllowed {
		t.Errorf("Expected status 405, got %d", rr.Code)
	}
}

func TestHandler_ServeHTTP_WebSocketUpgrade(t *testing.T) {
	hub := NewHub(DefaultHubConfig())
	go hub.Run()
	defer hub.Stop()

	handler := NewHandler(hub, nil)

	// Create test server
	server := httptest.NewServer(handler)
	defer server.Close()

	// Connect via WebSocket
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http")
	conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	if err != nil {
		t.Fatalf("Failed to connect to WebSocket: %v", err)
	}
	defer conn.Close()

	// Should be able to read welcome message
	_, message, err := conn.ReadMessage()
	if err != nil {
		t.Fatalf("Failed to read welcome message: %v", err)
	}

	// Parse welcome message
	msg, err := ParseMessage(message)
	if err != nil {
		t.Fatalf("Failed to parse welcome message: %v", err)
	}

	if msg.Type != "welcome" {
		t.Errorf("Expected welcome message, got %s", msg.Type)
	}

	// Verify client was registered
	time.Sleep(10 * time.Millisecond)
	if hub.GetClientCount() != 1 {
		t.Errorf("Expected 1 client to be registered, got %d", hub.GetClientCount())
	}
}

func TestHandler_ServeHTTP_MaxClientsExceeded(t *testing.T) {
	// Create hub with very low client limit
	hubConfig := DefaultHubConfig()
	hubConfig.MaxClients = 1
	hub := NewHub(hubConfig)
	go hub.Run()
	defer hub.Stop()

	handler := NewHandler(hub, nil)
	server := httptest.NewServer(handler)
	defer server.Close()

	wsURL := "ws" + strings.TrimPrefix(server.URL, "http")

	// Connect first client
	conn1, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	if err != nil {
		t.Fatalf("Failed to connect first client: %v", err)
	}
	defer conn1.Close()

	time.Sleep(10 * time.Millisecond)

	// Try to connect second client (should be rejected or fail)
	conn2, resp, err := websocket.DefaultDialer.Dial(wsURL, nil)
	if err == nil && conn2 != nil {
		// If connection succeeds, it should be closed immediately by the server
		conn2.Close()
		// Wait a bit to see if the server closes it
		time.Sleep(50 * time.Millisecond)
		t.Log("Second connection was established but should be limited by server")
	} else {
		// Connection failed - this is also acceptable
		t.Logf("Second connection failed as expected: %v", err)
	}

	if resp != nil {
		t.Logf("Response status: %d", resp.StatusCode)
	}
}

func TestHandler_GenerateClientID(t *testing.T) {
	hub := NewHub(DefaultHubConfig())
	handler := NewHandler(hub, nil)

	// Test without user ID
	req := httptest.NewRequest("GET", "/ws", nil)
	clientID := handler.generateClientID(req)

	if clientID == "" {
		t.Error("Expected client ID to be generated")
	}

	if !strings.HasPrefix(clientID, "client-") {
		t.Errorf("Expected client ID to start with 'client-', got %s", clientID)
	}

	// Test with user ID
	req.Header.Set("X-User-ID", "user123")
	userClientID := handler.generateClientID(req)

	if !strings.HasPrefix(userClientID, "user123-") {
		t.Errorf("Expected client ID to start with 'user123-', got %s", userClientID)
	}
}

func TestHandler_WebSocketStatsHandler(t *testing.T) {
	hub := NewHub(DefaultHubConfig())
	handler := NewHandler(hub, nil)

	req := httptest.NewRequest("GET", "/api/ws/stats", nil)
	rr := httptest.NewRecorder()

	handler.WebSocketStatsHandler(rr, req)

	if rr.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", rr.Code)
	}

	contentType := rr.Header().Get("Content-Type")
	if contentType != "application/json" {
		t.Errorf("Expected Content-Type application/json, got %s", contentType)
	}
}

func TestCORSMiddleware(t *testing.T) {
	allowedOrigins := []string{"http://localhost:3000", "https://example.com"}
	middleware := CORSMiddleware(allowedOrigins)

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})

	wrappedHandler := middleware(handler)

	// Test allowed origin
	req := httptest.NewRequest("GET", "/ws", nil)
	req.Header.Set("Origin", "http://localhost:3000")
	rr := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(rr, req)

	if rr.Header().Get("Access-Control-Allow-Origin") != "http://localhost:3000" {
		t.Error("Expected CORS header to be set for allowed origin")
	}

	// Test disallowed origin
	req2 := httptest.NewRequest("GET", "/ws", nil)
	req2.Header.Set("Origin", "http://malicious.com")
	rr2 := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(rr2, req2)

	if rr2.Header().Get("Access-Control-Allow-Origin") == "http://malicious.com" {
		t.Error("Expected CORS header not to be set for disallowed origin")
	}

	// Test wildcard origin
	wildcardMiddleware := CORSMiddleware([]string{"*"})
	wildcardHandler := wildcardMiddleware(handler)

	req3 := httptest.NewRequest("GET", "/ws", nil)
	req3.Header.Set("Origin", "http://any-origin.com")
	rr3 := httptest.NewRecorder()

	wildcardHandler.ServeHTTP(rr3, req3)

	if rr3.Header().Get("Access-Control-Allow-Origin") != "http://any-origin.com" {
		t.Error("Expected CORS header to be set for any origin with wildcard")
	}
}

func TestCORSMiddleware_OPTIONS(t *testing.T) {
	middleware := CORSMiddleware([]string{"*"})

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		t.Error("Handler should not be called for OPTIONS request")
	})

	wrappedHandler := middleware(handler)

	req := httptest.NewRequest("OPTIONS", "/ws", nil)
	rr := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(rr, req)

	if rr.Code != http.StatusOK {
		t.Errorf("Expected status 200 for OPTIONS request, got %d", rr.Code)
	}

	if rr.Header().Get("Access-Control-Allow-Methods") == "" {
		t.Error("Expected Access-Control-Allow-Methods header to be set")
	}
}

func TestAuthMiddleware(t *testing.T) {
	// Mock authentication function
	authFunc := func(r *http.Request) (bool, string, error) {
		token := r.Header.Get("Authorization")
		if token == "Bearer valid-token" {
			return true, "user123", nil
		}
		return false, "", nil
	}

	middleware := AuthMiddleware(authFunc)

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		userID := r.Header.Get("X-User-ID")
		if userID != "user123" {
			t.Errorf("Expected X-User-ID to be set to 'user123', got %s", userID)
		}
		w.WriteHeader(http.StatusOK)
	})

	wrappedHandler := middleware(handler)

	// Test valid authentication
	req := httptest.NewRequest("GET", "/ws", nil)
	req.Header.Set("Authorization", "Bearer valid-token")
	rr := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(rr, req)

	if rr.Code != http.StatusOK {
		t.Errorf("Expected status 200 for valid auth, got %d", rr.Code)
	}

	// Test invalid authentication
	req2 := httptest.NewRequest("GET", "/ws", nil)
	req2.Header.Set("Authorization", "Bearer invalid-token")
	rr2 := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(rr2, req2)

	if rr2.Code != http.StatusUnauthorized {
		t.Errorf("Expected status 401 for invalid auth, got %d", rr2.Code)
	}
}

func TestLoggingMiddleware(t *testing.T) {
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})

	wrappedHandler := LoggingMiddleware(handler)

	// Test WebSocket upgrade request
	req := httptest.NewRequest("GET", "/ws", nil)
	req.Header.Set("Upgrade", "websocket")
	req.Header.Set("User-Agent", "Test-Agent/1.0")
	rr := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(rr, req)

	if rr.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", rr.Code)
	}

	// Test non-WebSocket request
	req2 := httptest.NewRequest("GET", "/api/health", nil)
	rr2 := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(rr2, req2)

	if rr2.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", rr2.Code)
	}
}

func TestRateLimitMiddleware(t *testing.T) {
	middleware := RateLimitMiddleware(2, 100*time.Millisecond)

	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})

	wrappedHandler := middleware(handler)

	// First request should succeed
	req1 := httptest.NewRequest("GET", "/ws", nil)
	req1.RemoteAddr = "127.0.0.1:12345"
	rr1 := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(rr1, req1)

	if rr1.Code != http.StatusOK {
		t.Errorf("Expected status 200 for first request, got %d", rr1.Code)
	}

	// Second request should succeed
	req2 := httptest.NewRequest("GET", "/ws", nil)
	req2.RemoteAddr = "127.0.0.1:12345"
	rr2 := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(rr2, req2)

	if rr2.Code != http.StatusOK {
		t.Errorf("Expected status 200 for second request, got %d", rr2.Code)
	}

	// Third request should be rate limited
	req3 := httptest.NewRequest("GET", "/ws", nil)
	req3.RemoteAddr = "127.0.0.1:12345"
	rr3 := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(rr3, req3)

	if rr3.Code != http.StatusTooManyRequests {
		t.Errorf("Expected status 429 for rate limited request, got %d", rr3.Code)
	}
}
