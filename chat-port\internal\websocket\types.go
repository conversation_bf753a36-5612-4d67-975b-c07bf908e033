package websocket

import (
	"encoding/json"
	"time"
)

// MessageType represents different types of WebSocket messages
type MessageType string

const (
	// Incoming message types (from server to client)
	MessageTypeIncomingWhatsApp MessageType = "incoming_whatsapp"
	MessageTypeStatusUpdate     MessageType = "status_update"
	MessageTypeMetricsUpdate    MessageType = "metrics_update"
	MessageTypeHealthUpdate     MessageType = "health_update"
	MessageTypeError            MessageType = "error"
	MessageTypePing             MessageType = "ping"
	MessageTypePong             MessageType = "pong"
	
	// Outgoing message types (from client to server)
	MessageTypeSubscribe        MessageType = "subscribe"
	MessageTypeUnsubscribe      MessageType = "unsubscribe"
	MessageTypeHeartbeat        MessageType = "heartbeat"
)

// SubscriptionType represents different subscription types
type SubscriptionType string

const (
	SubscriptionWhatsApp SubscriptionType = "whatsapp"
	SubscriptionStatus   SubscriptionType = "status"
	SubscriptionMetrics  SubscriptionType = "metrics"
	SubscriptionHealth   SubscriptionType = "health"
	SubscriptionAll      SubscriptionType = "all"
)

// Message represents a WebSocket message
type Message struct {
	Type      MessageType     `json:"type"`
	Timestamp time.Time       `json:"timestamp"`
	Data      json.RawMessage `json:"data,omitempty"`
	Error     string          `json:"error,omitempty"`
	ID        string          `json:"id,omitempty"`
}

// IncomingWhatsAppMessage represents an incoming WhatsApp message
type IncomingWhatsAppMessage struct {
	From      string    `json:"from"`
	Message   string    `json:"message"`
	Timestamp time.Time `json:"timestamp"`
	MessageID string    `json:"message_id"`
}

// StatusUpdate represents a service status update
type StatusUpdate struct {
	Service   string    `json:"service"`
	Status    string    `json:"status"`
	Details   string    `json:"details,omitempty"`
	Timestamp time.Time `json:"timestamp"`
}

// MetricsUpdate represents real-time metrics
type MetricsUpdate struct {
	MessagesReceived      int64     `json:"messages_received"`
	MessagesSent          int64     `json:"messages_sent"`
	MessagesFailed        int64     `json:"messages_failed"`
	AIServiceCalls        int64     `json:"ai_service_calls"`
	AIServiceFailures     int64     `json:"ai_service_failures"`
	HTTPRequests          int64     `json:"http_requests"`
	HTTPErrors            int64     `json:"http_errors"`
	WhatsAppReconnects    int64     `json:"whatsapp_reconnects"`
	MessageSuccessRate    float64   `json:"message_success_rate"`
	AIServiceSuccessRate  float64   `json:"ai_service_success_rate"`
	HTTPSuccessRate       float64   `json:"http_success_rate"`
	Timestamp             time.Time `json:"timestamp"`
}

// HealthUpdate represents health status changes
type HealthUpdate struct {
	Status         string                 `json:"status"`
	Services       map[string]interface{} `json:"services"`
	Timestamp      time.Time              `json:"timestamp"`
	PreviousStatus string                 `json:"previous_status,omitempty"`
}

// SubscriptionRequest represents a client subscription request
type SubscriptionRequest struct {
	Action       string             `json:"action"` // "subscribe" or "unsubscribe"
	Subscription SubscriptionType   `json:"subscription"`
	Filters      map[string]string  `json:"filters,omitempty"`
}

// ErrorMessage represents an error message
type ErrorMessage struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// HeartbeatMessage represents a heartbeat message
type HeartbeatMessage struct {
	ClientID  string    `json:"client_id"`
	Timestamp time.Time `json:"timestamp"`
}

// NewMessage creates a new WebSocket message
func NewMessage(msgType MessageType, data interface{}) (*Message, error) {
	var jsonData json.RawMessage
	var err error
	
	if data != nil {
		jsonData, err = json.Marshal(data)
		if err != nil {
			return nil, err
		}
	}
	
	return &Message{
		Type:      msgType,
		Timestamp: time.Now(),
		Data:      jsonData,
	}, nil
}

// NewErrorMessage creates a new error message
func NewErrorMessage(code, message, details string) (*Message, error) {
	errorData := ErrorMessage{
		Code:    code,
		Message: message,
		Details: details,
	}
	
	return NewMessage(MessageTypeError, errorData)
}

// NewIncomingWhatsAppMessage creates a new incoming WhatsApp message
func NewIncomingWhatsAppMessage(from, message, messageID string) (*Message, error) {
	data := IncomingWhatsAppMessage{
		From:      from,
		Message:   message,
		Timestamp: time.Now(),
		MessageID: messageID,
	}
	
	return NewMessage(MessageTypeIncomingWhatsApp, data)
}

// NewStatusUpdate creates a new status update message
func NewStatusUpdate(service, status, details string) (*Message, error) {
	data := StatusUpdate{
		Service:   service,
		Status:    status,
		Details:   details,
		Timestamp: time.Now(),
	}
	
	return NewMessage(MessageTypeStatusUpdate, data)
}

// NewMetricsUpdate creates a new metrics update message
func NewMetricsUpdate(metrics MetricsUpdate) (*Message, error) {
	metrics.Timestamp = time.Now()
	return NewMessage(MessageTypeMetricsUpdate, metrics)
}

// NewHealthUpdate creates a new health update message
func NewHealthUpdate(status string, services map[string]interface{}, previousStatus string) (*Message, error) {
	data := HealthUpdate{
		Status:         status,
		Services:       services,
		Timestamp:      time.Now(),
		PreviousStatus: previousStatus,
	}
	
	return NewMessage(MessageTypeHealthUpdate, data)
}

// ParseMessage parses a raw WebSocket message
func ParseMessage(data []byte) (*Message, error) {
	var msg Message
	err := json.Unmarshal(data, &msg)
	if err != nil {
		return nil, err
	}
	
	return &msg, nil
}

// ParseSubscriptionRequest parses a subscription request from message data
func ParseSubscriptionRequest(data json.RawMessage) (*SubscriptionRequest, error) {
	var req SubscriptionRequest
	err := json.Unmarshal(data, &req)
	if err != nil {
		return nil, err
	}
	
	return &req, nil
}

// ParseHeartbeat parses a heartbeat message from message data
func ParseHeartbeat(data json.RawMessage) (*HeartbeatMessage, error) {
	var hb HeartbeatMessage
	err := json.Unmarshal(data, &hb)
	if err != nil {
		return nil, err
	}
	
	return &hb, nil
}

// ToJSON converts a message to JSON bytes
func (m *Message) ToJSON() ([]byte, error) {
	return json.Marshal(m)
}

// GetData unmarshals the message data into the provided interface
func (m *Message) GetData(v interface{}) error {
	if m.Data == nil {
		return nil
	}
	
	return json.Unmarshal(m.Data, v)
}

// IsValid checks if the message is valid
func (m *Message) IsValid() bool {
	return m.Type != "" && !m.Timestamp.IsZero()
}

// IsSubscriptionMessage checks if the message is a subscription-related message
func (m *Message) IsSubscriptionMessage() bool {
	return m.Type == MessageTypeSubscribe || m.Type == MessageTypeUnsubscribe
}

// IsHeartbeatMessage checks if the message is a heartbeat message
func (m *Message) IsHeartbeatMessage() bool {
	return m.Type == MessageTypeHeartbeat || m.Type == MessageTypePing || m.Type == MessageTypePong
}

// ValidSubscriptionTypes returns all valid subscription types
func ValidSubscriptionTypes() []SubscriptionType {
	return []SubscriptionType{
		SubscriptionWhatsApp,
		SubscriptionStatus,
		SubscriptionMetrics,
		SubscriptionHealth,
		SubscriptionAll,
	}
}

// IsValidSubscriptionType checks if a subscription type is valid
func IsValidSubscriptionType(sub SubscriptionType) bool {
	for _, valid := range ValidSubscriptionTypes() {
		if sub == valid {
			return true
		}
	}
	return false
}
